/**
 * This class was created by <Vazkii>. It's distributed as
 * part of the Botania Mod. Get the Source Code in github:
 * https://github.com/Vazkii/Botania
 * 
 * Botania is Open Source and distributed under a
 * Creative Commons Attribution-NonCommercial-ShareAlike 3.0 License
 * (http://creativecommons.org/licenses/by-nc-sa/3.0/deed.en_GB)
 * 
 * File Created @ [Jan 22, 2014, 7:12:28 PM (GMT)]
 */
package vazkii.botania.api.subtile;

/**
 * The special flowers in botania implement this. Used for cases where
 * BlockFlower would be checked against, but isn't convenient for
 * the special flowers with effects. For Azanor and Lycaon.
 */
public interface ISpecialFlower {

}
