/*******************************************************************************
 * Copyright 2011-2014 SirSengir
 * 
 * This work (the API) is licensed under the "MIT" License, see LICENSE.txt for details.
 ******************************************************************************/
package forestry.api.core;

import net.minecraft.creativetab.CreativeTabs;

/**
 *  References to the specialised tabs added by Forestry to creative inventory. 
 */
public class Tabs {

	public static CreativeTabs tabApiculture;
	public static CreativeTabs tabArboriculture;
	public static CreativeTabs tabLepidopterology;

}
