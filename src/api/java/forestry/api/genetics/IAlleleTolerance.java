/*******************************************************************************
 * Copyright 2011-2014 SirSengir
 * 
 * This work (the API) is licensed under the "MIT" License, see LICENSE.txt for details.
 ******************************************************************************/
package forestry.api.genetics;

/**
 * Simple interface to allow adding additional alleles containing float values.
 */
public interface IAlleleTolerance extends IAllele {

	EnumTolerance getValue();

}
