/*******************************************************************************
 * Copyright 2011-2014 SirSengir
 * 
 * This work (the API) is licensed under the "MIT" License, see LICENSE.txt for details.
 ******************************************************************************/
package forestry.api.genetics;

/**
 * Simple interface to allow adding additional alleles containing integer values.
 */
public interface IAlleleInteger extends IAllele {

	int getValue();

}
