/*******************************************************************************
 * Copyright 2011-2014 SirSengir
 * 
 * This work (the API) is licensed under the "MIT" License, see LICENSE.txt for details.
 ******************************************************************************/
package forestry.api.lepidopterology;

public enum EnumFlutterType {
	BUTTERFLY,
	SERUM,
	CATERPILLAR,
	NONE;
	
	public static final EnumFlutterType[] VALUES = values();
}
