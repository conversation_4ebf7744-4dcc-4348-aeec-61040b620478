//TITLE A classic tragic backstory
My name is <PERSON><PERSON><PERSON>. I was a kid when the demons came for my village during The Wars. They ransacked the houses and turned the shacks into splinters, wielding fire and water to blast the land asunder. I woke up to some travelling merchants that were passing by, equipping the warriors who were futily trying to drive off the demons that still clawed the village.
I was brought to a village nearby, where a magician named <PERSON><PERSON> helped tend to my wounds. The magic that he used was something that I had never seen before � it wasn�t Thaumaturgy, nor Alchemy, and it was definitely not Botany. He winked at me once he saw that my eyes were open, holding his finger to his lips.

Fast-forward several years, and I have learned almost everything from Master <PERSON>, being his third student ever to master his arts. Against his wishes, I have recorded my research and put several wards and spells on this book. So welcome, apprentice. I am known as The Architect, and I am a Blood Mage.

It took several years of pestering before I managed to convince <PERSON><PERSON> to teach me. He kept on telling me that, �Magic that uses the life essence of living beings requires patience and preparation in order to master it. One false move, go a little past your natural endurance, and you may find yourself taking a nice vacation in Tartarus.� The thing was, I wanted to go there � I had some unfinished business with the demons.

The process that <PERSON><PERSON> originally constructed required powerful artifacts that he constructed himself, but were rather lacking where teaching was concerned. After studying a bit of alchemy and the process of �Equivalent Exchange,� I managed to construct myself an altar that would transmute items inside of its basin into new powerful forms. The only issue was that it needed a worthy catalyst, and so with a prick of the finger I set the Blood Altar alight!

//TITLE The Blood Altar
//ITEM AWWayofTime:Altar
//ITEM AWWayofTime:sacrificialKnife
To start any form of transmutation involving blood, you would need to construct a blood altar and a sacrificial knife, as well as have a solitary diamond in your possession. After placing the blood altar down, Magus advised me to be careful as I filled it slowly with my blood, and said that I would need to be really close to the altar (about a metre) for the knife to work. With about 2 buckets of blood in the altar, which Master Magus reminds me is about 10 hearts worth, I placed the diamond inside of the altar by activating it with the diamond in hand.

//ITEM AWWayofTime:weakBloodOrb
The blood dissipated in a cloud of red swirls as I waited for the atoms of the diamond to shift and reform. There were a few moments where the particles turned gray, which meant that the altar was empty and I had to hurry to fill it. After the diamond burst in a shower of red particles, what finally sat in the altar was a Weak Blood Orb.

//TITLE The Soul Network

One thing that I initially didn�t understand was the overarching connection between the blood orb and myself. When I initially met Magus, I could see many sparkling strands branching off of him, flowing throughout his house and linking with intricate stones and runic drawings. I asked Magus about the strands, but he had no clue what I was talking about. It took three years of thorough research to finally find the answer, and when I brought my notes to him he was really impressed with what I have found.

When you send power into the orb, the energy is transmitted from the strand connecting the orb and into the very soul of the person the orb is bound to. Similarly, and Magus managed to show this effect with several of his rituals, when you use something that drains energy it will drain the energy directly from the soul. The thing is that if you use an item whose owner has no energy left, the item will instead grab the requisite energy from the user of the item. Directly. From his or her life force. As such, the unit of measurement is called �Life Points,� or LP. I experimented with this, and one heart equals 200 LP.

I have christened this system to be the �Soul Network,� and is used in all branches of Blood Magic indirectly.

//TITLE Basics of sigils
//ITEM AWWayofTime:blankSlate
Magus is a master at rituals. His power in the intricate layering of stones and inks is unmatched. The problem is that these rituals are rather� static in nature. Sure, being able to call the bounty of the earth to the surface is all fine and dandy, but won�t exactly protect you when you are on fire. To this end, I decided to link my soul network to powerful items that I have created. To start, I decided to transmute a piece of smooth stone in the Blood Altar with just 1kLP to create a blank slate.
The first thing I did was to arrange the blank slate with some reflective glass and my weak blood orb. Pouring my power into the configuration created a Divination Sigil, which I could link to my network and see how much power that my soul network holds. What is more, holding the sigil to the Blood Altar flooded my mind with information, giving me the knowledge about its current tier, capacity, and even how much it was holding.
Happy with the sigil, I brought a fresh unbound one to Master Magus for him to use. When I took the divination sigil back in my hands and tried to use it to view his network, for some reason I could not gleam any information from him. I don�t really see why this is, considering that I used this same method for other people and I saw that they had no power at all, but to gleam actually � nothing from Magus is strange.

//TITLE Training, and water sigil
//ITEM AWWayofTime:waterSigil
I�ve been training my body, lately. Master Magus told me that in order to extract most of my power, I have to get my mind, soul, and body into as perfect shape as possible. Even though my mind takes time to hone, and my soul requires methods I have yet mastered to strengthen, my body is something I can actively manage. No more cookies for me, apparently, since they have horrible saturation for the work I do. And it�s hard to grow cocoa right now, anyways.
The temperature is ridiculously high, and with it the humidity is pretty freaking low. Some bright spark of a thaumaturge got it into their head that it would be good to open up the Obelisk to the far north of the village, and because of it a large influx of taint spewed forth from the eldritch tomb. It looks like someone wasn�t focusing enough on their sanity training! Thankfully Master Magus decided to head out to the site up north with a few stones and a new vat of ink, saying over his shoulder that he had a new ritual that he�d like to test to fix the problem.

In the meantime, I was tasked to create something to fix our water problem. I thought about perhaps cooling the environment around the village in order to condense the water in the air, or perhaps find a way to create rain � neither of which I would be able to accomplish, because I�m sure that either task would take more than the 5 kLP capacity I currently have with my blood orb. So, I had to think small.

At my desk, I mixed some dirt into the spare water I had for experimentation and started to paint on one of the blank slates I recently created to layer a bit of a base. Once I was satisfied, I took a bunch of buckets (to be safe in case that what I did would fail) and arranged them around the damp slate and then took my orb from the chest nearby. I then tapped the orb onto the slate, and the water started to boil and swirl out of the surrounding buckets, rushing into the slate! After a quick, bright flash, the new sigil lay steaming on the table in a puddle of water.

Thankfully the sigil was what I needed. I could link it to my network and place water wherever I wanted by simply tapping it on the ground. The water was pricy, requiring a full 100 LP for each bucket�s worth that I created in the world, but for an infinite supply of water, creeping upwards to 50 full buckets for my puny network was something that I desperately needed. It�s just a shame that it started to rain an hour after I created the sigil.

//TITLE The Lava crystal
//ITEM AWWayofTime:lavaCrystal
There was an explosion in the lab today as I was trying to work out a few more artifacts. When I tried to infuse a few buckets of lava with my power, and used some glass to contain the effect, the assembly simply burst into flames and flew me across the workshop. I wasn�t really hurt, and thankfully my water sigil was used promptly to put out the fires, however it seemed that my experiment failed rather heartily.

Apparently I needed just a little bit more time, however, since I got it to work a week after. By combining some glass and obsidian together with a few buckets of lava, and throwing in my blood orb and a diamond to hold it all together, the diamond was transmuted into a fiery crystal that seemed to� do nothing at all.

I peeked out from behind the blast shield, since by then I learned that playing with lava was not the safest of hobbies, and gingerly linked the crystal to my network. As soon as the pact was completed, I felt a wave of heat emanate from the crystal, and promptly tossed it into a furnace before it could burn my fingers. I was unsuccessful on that front. The furnace immediately kindled, and the pork that was sitting in the top slot began to cook quickly.
Quickly feeling a wave of nausea, I checked my network out and found that I had nothing left to offer. Once I pulled the blasted thing out of the furnace and stuffed it into a chest, the nausea subsided. So this Lava Crystal could work in any sort of furnace and feed off of the owner�s network, but if the network was dry it would instead cause the owner to feel a hearty blast of nausea unless removed from the solid fuel-burning machine. Good to know, since now I know of a way to trade my services with the blacksmiths in the village.

//TITLE Sigil of lava
//ITEM AWWayofTime:lavaSigil
Finally, the day that I have been waiting for the past 7 months has come. The �Hell�s Harvest Festival� is a day that the village celebrates the creation of the first portal to the Nether a century ago. It�s funny that when I ask the villagers why it is that we celebrate the formation of a portal to Hell, they simply laugh and say that I�ll see when I finally go in.
The portal was located in a vast stone cathedral, a three day�s walk from the village on foot. Magus said that he�d meet us there, because he had a few things that he needed to prepare before actually going over to the Nether, but I was curious as to how he�d make it there in time � knowing Master Magus, he�d spend a day or two reading up on a new theory for a ritual and forget himself in his works.

When we entered the hall, however, Master Magus was there already talking to one of the guards of the unlit portal, stowing away a gem into his pocket. I knew something was up, because I could see a strand connecting the gem in his pocket to both himself and one that trailed off back to the village. Not wanting to be distracted, I pulled out my wand (Master Magus insists on all of his apprentices learning of another field of magic, and Thaumaturgy has proved itself useful) and set alight the portal as was the custom of the newest member to the harvest.

It seems that the harvest, however, was not exactly a trade. I saw giant stone walls and vast pumps throughout the land, grabbing all of the lava from the great pool below. A large collection of bound pigmen walked by, led away by someone in a full set of shining steel armour. Huge machines that processed the lava were emblazoned by the brands of industrialization: Thermal Expansion, BuildCraft, Greg Tech, and many other labels dotted throughout the base of operations. So much for a festival � it seemed that the Conglomeration, the governing body that oversaw all of the villages in the land, saw it fit to turn a civilization into a circus.

After accepting a few supplies from the farce, I walked back home by myself two days early. There were some things that I wanted to create, but wasting my time there would have been fruitless. Clearing my desk of debris charts of alchemical constructs, I set to work with a vat of magma cream that I brought with me, attempting to combine it with another slate. Casting my orb aside, I fished the lava crystal from my chest and combined it with the cream and some fresh buckets of lava � thankfully not from the Nether. Once the light faded, I picked up the new Lava Sigil that lay in front of me.
What this sigil does is it siphons energy and uses the properties of the lava crystal to super heat rock, creating a source of lava wherever the wielder touches the ground. So, infinite lava, and infinite fire hazards. Unfortunately, it takes about 1 kLP per single use, which is a lot for my network as it stands currently. It looks like I need more power, but seeing that Master Magus is currently enjoying himself at the festival, I�m going to have to find a way to boost the blood altar myself.

//TITLE Blank runes
//ITEM AWWayofTime:AlchemicalWizardrybloodRune
Of course! Runes! Why didn�t I think of this earlier? The power of the altar itself comes from the structure of the components used to create it. The force of the diamond alone is not enough to create stronger materials through transmutation. What I need to do is construct several runes to extend the altar, and thus the altar�s power. The only thing that I could do on such short notice is to take several blank slates (I have about 16 left) and quite a bit more stone and combine them with my Weak Blood orb.

//IMAGE 8 854 480 alchemicalwizardry:textures/misc/screenshots/altars/T2.png

What came next was the hard part. After I made 8 runes for myself, I sat puzzling over the altar. �How could I extend this thing?� I asked myself. It took a few hours, but when I placed the 8 runes in the ground around the altar in a ring, I felt a strange reverberation in the air. Something felt like it awoke, as if the power in the surrounding environment doubled. Quickly, I grabbed my Divination Sigil and slapped it onto the altar, and focused into it to view the power of the altar.

I was pleased to find out that the altar was finally at its second tier, which should open up many more transmutation recipes. The problem now, as I sank slowly into my bed to sleep after the long day, was to find out what I could now do.

Although Magus was pleased with the upgraded altar, I was caught in a bind. I couldn�t seem to do anything with it that the previous altar could not do. The first thing I did was I placed one of my leftover blank slates into the basin, and it promptly consumed 2 kLP worth of essence as it reinforced the structure. Although this Reinforced Slate was much more durable, this process appeared to make the slate no longer work with creating both the lava and water sigils, as well as making it a waste to use in additional rune creation.

//TITLE Speed runes
//ITEM AWWayofTime:speedRune
I can note, however, that when I combined one of the runes with a few more blank slates and some leftover sugar from the harvest it seemed to augment the altar even further. The sugar had to be laid in a configuration that mimicked a bow and arrow on top of the rune, and by doing so it created what I can dub a Speed Rune. What this rune appeared to do was increase the speed of the altar�s processes, both the transmutation rate as well as the speed that a bound orb will consume the essence from the altar, by an additive 20% per rune. This tier doesn�t seem perfect, however, since it seems that for the second tier only the four edge runes (not the corners) could be used as upgrade �slots� for the purpose of augmentation of the altar.

//TITLE A shining green orb
//ITEM AWWayofTime:apprenticeBloodOrb
The �eureka� moment came when I accidentally dropped a few things on the floor of the lab. When I bent down to phish the items from the floor I noticed that one of the emeralds rolled right up to the altar, almost as if it longed to be inside of its basin. It occurred to me that if a simple diamond inside the altar created a weak blood orb, perhaps a much rarer gem would make a stronger version! Quickly fetching my knife from the table, I filled up the altar over the course of the day so that I could make sure I didn�t mess anything up, and then dropped the emerald into the altar.
The speed runes made sure that the transmutation took no time at all! Wow, those things work fast � I better make a note of removing the runes when I try to create more difficult items inside of the altar, because if I was not prepared it would have chewed through my supply in no time! After a few tantalizing moments waiting, the altar burst in a shower of particles and the new orb sat there, revolving on top of the altar.
Master Magus must have heard my shout of exclamation, because he hurried over from his side of the house and helped me to my feet. Not noticing the pain in my palm from earlier, I reached for the orb and sealed it to my network. Master Magus then helped me to fill up the orb inside of the altar, and when we were done that day I had a total of 25 kLP inside of my network.

//TITLE The void sigil
//ITEM AWWayofTime:voidSigil
After resting the rest of the day at the insistence of Magus, I returned to work this morning full of inspiration. While I laid in bed half awake, the thought struck me that so far I had managed to use sigils to create fluid. So why not make one that can remove it? My first attempts with things like glass bottles and bowls failed with all of them working for a brief moment, then returning to their old forms. It was almost lunch time when the thought crossed me that I used buckets for the first two, so why not here? Half an hour, six buckets, a bit of twine and a reinforced slate later I had my first void sigil.


//TITLE Air sigil
//ITEM AWWayofTime:airSigil
While tending to our small farm a few days later, I noticed a bird land on our fence. I turned to face it as it flied off, and had a revelation as I watched how it flew. If I could use magic to create the gust of wind like that made by bird wings, I could fly through the air! After hastily finishing the days� chores, working out details in my head all the while, I rushed to my workstation to start work on what I have now dubbed the �Air Sigil�. Using feathers and a Ghast tear as ink on a reinforced slate, I formed a sigil that on use creates a rush of air behind where you face to propel you forward. Flight using this sigil is fast, if a little pricey and hard to control, not like the flight provided by Magus's thaumostatic harness. Also, landing is a bit of a problem as I learned the hard way...

//TITLE Sigil of Sight
//ITEM AWWayofTime:seerSigil
I've grown a little annoyed at my divination sigil. While it is able to read the current tier and essence of a blood altar, that is ALL it can read. As a result, my current goal is to improve its design using my new blood orb. So far it is going well. The �Sigil of Sight� is able to read the buffers of the altar to give how much they store, as well as how far along a craft in the altar is and how fast it is using LP. The downside of this upgrade is that it loses the power to read the altar's tier or the users soul network, but so far this is an unavoidable loss. For now I'll settle with what it does now, and might try to improve it further later.
	
//TITLE Advanced altar mechanics

Even after all this time, I still don't fully understand the blood altar. Sure, I built the thing, but... It is almost like working directly with raw life essence has given it a bit of life of its own. After running a few experiments to try and make my life easier, I've noticed some odd behavior coming from it.

First, I tried using some basic piping to extract life essence to store later to use for more pricey infusions. As it turns out, the altar doesn't like giving up its essence. I managed to get a bucket of LE out before it seemed to slow to a trickle feed, nothing more the 20LP a second. Further tests show that it has a �Buffer� tank that siphons off LP from the main tank, holding roughly 10% of the main tanks capacity. As I mentioned, it does this very slowly so piping is out of the question for now...

A later test shows that it also doesn't like essence being piped in, as a similar restriction apples to imputing essence. I will defiantly need to find a way to improve this transfer rate...

Another strange phenomenon about the altar is its �Hunger� for life. After the failure of the piping, I tried using a few regeneration potions from the local village to speed up the rate at which I recovered health. A normal or extended potion seemed fine, and were very effective for speeding up slate production. But a strengthened regeneration potion caused the altar to have a slight �Hum� about it, and I felt my energy being sapped by something. After talking with Magus about this, we have determined the altar senses the life flowing through us, and the potion makes the altars �Hunger� for this life create a sort of aura that causes you to lose energy akin to hunger. As a result of this, regeneration is still effective, but a sizable supply of food is needed for extended use to recover energy.

//TITLE Sigil of the fast miner
//ITEM AWWayofTime:sigilOfTheFastMiner
I've decided to experiment with passive effects for sigils, since everything so far has been an active effect. While wondering through town looking for supplies, I noticed people cutting wood for a fire and some smashing stones to build a small fire pit. I keep coming back to how slow both of them were in my train of thought, then started going over ways to improve the tools. Then it hit me: Why improve the tools when you can improve the person? A few anatomy books and failed tests later, I hand the sigil of the Fast miner(Name pending) and two very tired arms. After activating the sigil, it speeds up muscle movements in the arms allowing you to break blocks far faster at a small LP cost every few seconds. I do need to keep reminding myself to turn it off when not in use, but the stinging pain of life essence being taken is always a quick reminder...

//TITLE Soul Fray

Today I pushed myself too far. I've gotten close a few times, but today was the first time I have passed out due to draining too much life essence. Magus was thankfully with me at the time, and moved me to a bed to recover. As I regained my senses, I noticed something odd about my soul network thread: They were thin and looked damaged. As I kept watching my threads, it seemed like they were slowly reforming and repairing themselves. I mentioned this to Magus, and we theorized that after coming so close to death my soul was still recovering from the shock. I made a sacrifice at the altar to test this, and it seems that this state effects how much life essence a heart is worth quite negatively with it only giving a tenth the normal amount.
	
//TITLE Green grove
//ITEM AWWayofTime:growthSigil
Today Magus showed me his green grove ritual, a way to speed up crops. Unfortunately, it is very static and has a small range, limiting its uses. To amend this, I've been toying with the concepts behind the
ritual in an attempt to make it more portable. By creating an ink from oak saplings and sugarcane, then inscribing a rune on a reinforced slate I have created the sigil of the green grove. It seems a little slower and more expensive then the ritual form, but it can be carried on your person and acts like bonemeal when used on plants. While I had it active I could barely keep up with our farm, and by the end we needed to give a few of our neighbors some of the excess we didn't need. They all accepted it, but with varied degrees of reluctance.

//TITLE Dagger of sacrifice
//ITEM AWWayofTime:daggerOfSacrifice
My work on sigils has slowed for now as I'm reaching the limits of what my little green orb can do after only two weeks. As a result, I have turned my attentions to other aspects such as runes to pass the time. Magus is currently out dealing with a large forest fire to the east of us, and the village is in a bit of a lull with nothing planed for several weeks. Right now my task is to slaughter a few chickens Magus brought home from the market to prepare on his return home, and it seems the perfect time to test an idea I've been thinking about for a while... I will not mark it here, just in case it is another false hope.

Success! I've managed to perfect my idea for a new source of life essence. Before the only option was to cut ourselves to give our own life essence to the altar, but now we have a way to use other living things as a source. Unfortunately, it is a little gruesome and a bit �All or nothing� from them... Regardless, it works!

...I have realized that, in my excitement, I may have forgotten to write down what it is. I call it �The Dagger of Sacrifice�, formed in a blood altar out of an iron sword and 3K LP. The dagger doesn't deal much in the way of damage. It doesn't need to, as its main purpose is to �Mark� a living thing for the altar which takes care of the rest by forcibly extracting their life essence all at once. Through my tests it seems that your common farm animal's life isn't worth nearly as much as a person, only giving 250LP regardless of where it comes from. While I set about cleaning the animals after the tests were done, a common zombie managed to sneak in the door I left open when bringing the chickens down here. As soon as I noticed it, reflexes that were being drilled in by Magus every morning kicked in and I managed to poke it with my dagger before it had a chance to hurt me. At first I thought nothing happened, then it fell to the floor as lifeless as it should have already been. I glanced at the altar, quickly noting that a zombie was worth 500LP. Looking over the fallen creature as it started crumbling, I noticed that some of the defenses Magus had set up took there toll on the zombie as it was burned and freshly cut in several areas... Seems that the amount of HP they have is irreverent to the altar, giving the same amount of LP regardless.

As I finished preparing the meat to cook after disposing of the zombies ash(Which reminds me, I wanted to look up why this happens...), a very dark thought crossed my mind. The knife works by giving life to the altar. The dagger works by taking life forcibly from a target. What would happen if a human was marked by the dagger? I asked this to Magus, after showing him the dagger and telling him everything that happened. �In all honestly, I'm not sure. I have a theory, but I don't really feel like testing it...� I asked him to elaborate, so he told me the basics of it. It seemed that source of the life essence mattered with the dagger, since the chicken and zombie gave two different amounts, so depending on the person in question they would be split into one of three groups: Passive, Hostile, and Innocent. The passive would be like the chicken, only giving a little. The hostile would be the bandits, the thieves, the murderers. The ones who gave into evil, and now seek to hurt or steal from anyone. The unredeemed, and would probably be like the zombie. The final group were the innocents, anyone who doesn't fall into the first two groups. It was nothing but a guess, but we agreed that using the dagger on an Innocent villager would extract all of their life essence like ours. 2K LP, all at once... We also couldn't work out what would divide the passive from the rest, but it was only a theory so we put it to rest.

//TITLE Runes of Sacrifice
//ITEM AWWayofTime:runeOfSelfSacrifice
Looking at these little speed runes, I have decided to see what else can I make. My goal isn't anything in particular, just improving it in some way. The speed rune was made with powdered sugar, so another type of powder should work...

Redstone failed, gunpowder failed, glowstone... Glowstone worked. I currently have no idea what it does, all I know is it didn't fall to bits seconds after creating it so that is progress. The altar seems to accept it as a proper rune, so all that is left is to run a few tests in the morning. For now, I am tired and feel a bed calling me...

I've run dozens of tests trying to find out what this new rune does, after two days of obsessing over this I finally noticed it. It slightly increases the amount of LP self sacrifice gives. When I said slight, I mean one rune increases LP gained by ten percent on an additive rate. For those who don't understand that, it basically adds an additional twenty LP per self sacrifice. If my theory is right, I should be able to make a similar rune for normal sacrifice... What else would the altar respond to?

//ITEM AWWayofTime:runeOfSacrifice
Well, if there is anything thaumaturgy and magic in general has taught me, it is that gold is a very good metal for magic. By simply replacing the glowstone in the recipe with gold ingots, a sacrifice rune is formed. It has the same additional ten percent addition per rune per sacrifice, meaning the more LP of the mob sacrificed the more LP the runes adds. This has got me thinking... The altar was constructed with both diamond and gold. It seems to resonate with the diamond enough to make the first blood orb, and even a second, so why not try making one out of gold? I'll need to revisit this idea later as a test run showed some reaction to a block of solid gold(I didn't ask Magus where he got it, and he doesn't seem forthcoming about these matters), but the altar couldn't cope with the strain needed to shape the block. Before catastrophic failure happened, a few safety precautions Magus and I had built into the altar triggered, stopping the transmutation before... Well, neither of us are really sure what would happen. But Magus said he has dealt with matters of unstable and untested magic, and knew most of them end with a crater in the floor(Hence the safety precautions).

It has been a few days since the experiment with the gold block. Magus and I have been discussing what to do now, as the altar is getting bigger then the basement. Magus has suggested moving it to some of the lower basement floors, but considering just how large it might be in the future... I finally got him to agree to building it behind his home, as long as I helped put up a few wards and spells to keep people away. Currently we're planning out the structure and needed runes for upgrading. The current altar only has eight runes, but what I have planned will more then triple that number to twenty eight. The current limitation we suffer is the strength of the altar itself, as it can only take power from runes adjacent to it. My plan is to use something the altar is naturally attuned to as a way to not only increase the range of this effect, but act as a counter balance and stabilizing it to prevent it from becoming uncontrollable. For this to work, the blocks in question must be above the altar and an equal distance apart.

We've run several tests and calculations, and have a layout in mind for this upgrade. Four pillars topped with glowstone blocks, twenty runes in sections of five, built one block down and two out from the last set of runes. In theory this should provide a huge boost in power to the altar without risking our safety(Well, more then we normally risk it), but until we test full scale I can't be sure. For now, we have a lot of runes to make. After much debate we've decided to invest in the �Self sacrifice� runes, as having a steady supply of mobs will be tricky. But now I face the issue of making dozens of reinforced slates... I need to think on this.

//TITLE The blood letters pack
It's so simple! I've discovered a way of obtaining LP for making slates that means I don't wait at the altar all day: The Blood letter's Backpack. Made with a modified leather chest-piece with a blank slate reinforced glass tank, it allows the wearer to gather LP by feeding it into the pack over time. Whenever the wearer has more then five hearts, the pack will drain their life and store it inside up to 10K LP to be dumped into an altar later. This pack does have two shortcomings, however. When the life essence is deposited into the altar, runes will have no bonus to the amount added. The second problem is that the altar will only accept LP from the pack when not performing a transmutation. Despite these limitations, it will prove quite valuable for simply allowing me to step away from the altar to do other tasks.

This is a simple progress update, as it's been a few weeks since I last wrote in this journal. I've been making slow, but steady progress with assembling the slates needed for the runes and we're now halfway through. This isn't the only reason for the lack of entries, as I've been preoccupied with my research thorough thaumaturgy. An old friend of Magus has been visiting and happens to be a well versed thaumaturge, and he has been giving me pointers on how I could be doing better while Magus has been preoccupied in his library dealing with a mountain of paperwork. Now that Master Ridden has returned home, I hope to progress farther in making runes in the next few days.

//TITLE And then there was five
Or not, as it seems Fate has other far more interesting plans for us. Today we had a knock on our door while I was making the last of the runes, and after opening it was greeted by three apparent orphans. The two boys in the front looked like they were brothers, or at least close relatives. The girl hiding behind them on the other hand had a very different hair and eye color, so relation was unlikely at best. All of them were covered in dirt, clothing ripped, torn, and patched with various levels of skill. The one on the left had an old sword with a chipped blade on his side, was slightly thinner and taller then the his brother, had a few deep scars that looked recent, long and messy dark brown hair, and was looking at me with a kind poker face. The brother looked more muscled, and was staring at me with a very analytical eyes and a similar poker face, his hair was slightly shorter and lighter then his brother. From what I could see of the girl, she had a simple dress on that was almost caked in dirt and dried mud. The fabric near her elbows was becoming thread bare, and a large rip near the bottom of the dress exposed some of her right leg. Her hair was a light blond, almost pale white color, but the most shocking feature was her eyes... I haven't seen haunted eyes like that in years, and last time it was in a mirror. From the looks of them, all of them hadn't ate anything in days and seemed to have everything they own on their bodies or in the simple packs on their backs.

I asked them if there was anything they needed, to which the elder brother said they were looking for master Magus. I called Magus to the door, visitors being a rare thing as it stands with ones who ask for him by name and not clearly mages being rarer still. Five minutes later we all sat in the main room, eating simple sandwiches I had prepared for us all at the request of Magus. Needless to say, this day was not going as expected. Magus spent the next few minutes practically interrogating the elder brother , asking questions on both of our minds. As it turns out the two brothers, Vlad and Demier, use to be caravan traders, eldest a alchemist with basic knowledge of potion brewing, younger with enough mana and training already to cast basic spells. The girl on the other hand had a story all her own. From what the brothers had managed to gather, her village was raided like my own was and she was evidently the sole survivor. What shocked me was when they said she was found wandering the ashs, demons having left hours before without so much as a scratch on her body. Vlad was with the traders when they found her, and he was forced to leave them if he wanted to help the girl named Bella.

Their story seemed to win Magus over, as he told them he would try to do what he can for them.. Not long after that, Magus asked me to help him do some shopping for our new guests. On the way to the village, Magus asked me  what I thought of the three. �I can't say I believe all their story, but I also don't think their lying. Did you notice the girls eyes? Haven�t see a look like that since...�. Magus finished for me with �Your own?�. I nodded at the dark memories of that time. We continued going over the details of their story, but we agreed that just leaving them in the cold wasn't an option worth considering. We decided to at least try to keep our recent endeavors into blood magic a secret from them, but when push comes to shove I think we might have to let them in on the secret. Magus let out a long sigh. �It's been years since I had to juggle this many apprentices at once. I'm going to need your help training them. But something tell me that we're going to be learning as much from them as they will from us...� I asked him why he felt that way, and he said it was just a gut feeling. I must say I had a similar feeling. Things like this don't happen without reason, and of all mages to come to they stopped at our door, something far easier said then done.

An hour later we returned home, both our arms heavy with fabrics, food, and other necessities that we will need to take care of the new apprentices. I watched in amazement as the three ate, trying to make up for days with little food all at once. After that, Magus and I laid the three on various chairs and couches to rest, the strain of travel catching up with them. While Magus dug around looking for a few of his old things that we'd soon need, I started creating some new clothing from enchanted fabric for the three to replace their current worn and ratty clothing. Now that that is done, I should try to get some sleep. Something is telling me tomorrow is going to be quite busy.

The Early this morning Magus and I started carving out the earth for the three new bedrooms, since the old ones Magus use to have had since been converted into a storage closet, a alchemy lab, and small study. It didn't take nearly as long as I expected with Magus using a few spells to clear away earth while I went behind using an equal trade foci to transmute it into stone brick before gravity caught on. Once that was done, Magus raised a few walls to separate the space into three rooms and a hallway. Only after that was done did I notice the three children had awoken, and were watching us as we worked. Magus told them that he had some spare furniture in some storerooms they could use, but sadly only knew of one bed. It didn't take long for them to decide Bella got the bed, the brothers would make do with a pillow and a blanket on the floor.

After that we spent the rest of the day hunting for then moving furniture to their rooms, building little work areas and libraries for each of them. The following day was more of the same, with the evening filled with helping them find books to read from the grand library. Vlad grabbed as many alchemy books as possible, Demir took a few books on spell casting, and Bella took a Lexica botanica to read through. Later this evening, after the three had retired to their rooms, I sat in the main room with Magus, arms and legs aching from the heavy lifting of the last two days. I told Magus how I thought their rooms should be almost finished, and how I spent the last hour before they retired just answering seemingly endless questions. �I'm sure. I've gone through the same more then once, when I have apprentices new to magic. You were the same, I recall.� I opened my mouth to protest, but closed it as I thought back to those days. I decided I probably was as bad as them, and left it at that.

It's been just over a week, and the three have settled in nicely. They join Magus and I in morning exorcising, have started studying like true scholars, and have calmed the hungry nodes in their stomachs(Well, Vlad and Bella have. Demir on the other hand is another matter, but I feel he will always be like that.) Unfortunately, like true scholars and mages they've stumbled into places we didn't want them to go. Vlad has created a chemistry set that uses life essence from a blood orb as a fuel source, Demir has turned an old table into a way to use life essence to cast powerful spells, and Bella... She's left me questioning what I know about demons, after taming one and using a blood orb to bind it to her. All three of them have stumbled blindly into blood magic, and now I don't think we'll be able to stop them. Magus and I have agreed to give them a choice in whether they are blood mages or not, but I've seen the light in their eyes... I can't imagine any of them saying no. To seal their dedication to the art, we will have them make blood orbs of their own to use.

//TITLE The next step: Tier three
//IMAGE 8 854 480 alchemicalwizardry:textures/misc/screenshots/altars/T3.png
Even with all the chaos of the last week I've managed to finish the altar construction, and it has been more successful then I predicted. With full access to twenty eight runes, each heart is now worth over seven hundred LP. I've already started prepping to try again with the gold block, having a few instant healing potions at hand. I got a brief glimpse with my sigil of sight, and this transmutation will take twenty five thousand LP to finish, over double what the altar can hold! Writing this down does give me an idea, however.... I'll need to look into it further once I have finished making this.

//ITEM AWWayofTime:magicianBloodOrb

It was close, but I finished the orb today. I was down to the last of my three potions and barely had the strength left to hold the knife, but I pulled through and finished my shiny new blood orb! I've already used it to craft a few things I had made before, and extra lava and air sigil, and it handles it all with such ease! Now that this is finished, I have a small list of ideas to go through...

To start off the process, I want a stronger slate. As it stands the reinforced type is cracking under the strain I'm starting to place on it. Well, I got a new slate type when I last upgraded the altar... Let's see if it works again. It does! A reinforced slate in a tier three altar with around five thousand LP makes a new slate... I'll call it an imbued slate for now, and come up with a better name later.

//TITLE New runes
//ITEM AWWayofTime:AlchemicalWizardrybloodRune:1
First up to the drawing board: Rune of Capacity. The basic idea is simply to add more room to the altar, so a greater amount of LP can be stored at once to make bulk crafting or difficult crafts easier. Each rune adds 1K to the altar's main tank capacity, but what is interesting is that the �Buffer� tanks expend as well to a lesser degree. From a few trial tests(My back is starting to hurt from moving runes all day), the buffer tanks are always ten percent of the main tanks size. This will be something to keep in mind later.

After helping patch a hole in the wall caused by Demir accidentally unleashing a spell, then helping Vlad dispose of a few failed alchemical �Experiments�, I finally have an hour to myself to work on my ideas for runes. After the success of the last documented attempt, I've been on a bit of a failure streak. I've have runes fail to do anything, crumble to dust, blow up, and one attempt turned into cheese(I asked Magus about this, and said this isn't the first time he's seen that happen to a magical experiment. However he is just as stumped as I am over why it is always cheese, of all things). Thankfully, I finally found something that doesn't need a blast shield to use or starts growing mold after a while.

//ITEM AWWayofTime:AlchemicalWizardrybloodRune:2

Named the dislocation rune, it's only function is to increase the rate the life essence flows into and out of the buffers. Each rune increased the transfer rate by twenty percent per rune, and this effect stack manipulatively. That sounds great, until you realize the first rune only raises the transfer rate by four LP a second. Needless to say, your going to need around a dozen of these runes before you have any noticeable impact on the transfer rate. While these runes make it possible to automate the creation of slates by storing the life essence outside the altar, you're going to be sacrificing speed, capacity, and generation efficiency for this.

//TITLE Sigil of Magnetism
//ITEM AWWayofTime:sigilOfMagnetism
It's been a few weeks since I last made any headway with my blood magic research, needing to help with the tutoring of the Vlad, Demir and Bella along with my normal duties as a mage. I've had to leave twice this month in order to sort out some mess or another, whether is be a helping with a pack of wild wolves or a bad harvest. Today, however, I managed to get some inspiration for a new sigil. When I was in the market buy more food, I noticed two children playing with a rock that was slightly magnetic. They were just running around, seeing what it would stick too and cheering when it did, or seeing how close they could push it to something before it pulled itself closer... That is what sparked my idea: A sigil that acts as a magnet, pulling items off the ground closer to you. In theory I could rig the magic on it to apply a similar effect to all items and not just metallic ones...

Three hours later, and my sigil is finished. It needs a great deal of mass in the form of four iron blocks, along with the classic gold plating infused into an imbued slate(Drat, I never did work on a better name). This sigil needs to be toggled to activate, and while active will draw all nearby items to the user if they have room for it. Simple, but very effective....

//TITLE The Phantom Bridge
//ITEM AWWayofTime:sigilOfTheBridge
I've been asked to visit a small town to settle a few complaints that have been voiced lately by them, and am currently surprised that a closer bridge isn't one of them. I've had to walk three miles to cross this river, and now must walk another two tomorrow to reach them. If only I didn't need a bridge, or had thought to grab my air sigil(I still have a bruise from the last landing I had with it, so I wasn't keen on using it again so soon.) Wait... If if I had a bridge that went with me? Better yet, what if I had a bridge that formed under my feet as I walked? Yes, that would be amazing...

I'm back home after dealing with a feud over who really owned a cow(Answer: Neither of them, it wondered into town and they both thought it was one of their own), and have set out my work station to try and capitalize on my idea. I'd want the sigil to place blocks that are phantasmal, only there temporarily before fading into nothing after I leave. For that I'd need a special material... Soul sand might work... Add in a little stone for structuring around an imbued state, then use the orb... Done! Now then, for a test run... Drat, I left the auto-writing quill running again.

It works better then I hoped! The sigil creates a small five by five platform under me, staying at the same elevation as I walk through the air. Not only that, but I've tweaked it so that holding shift lowers the platform by one under you, and the blocks it makes can be easily broken for a quick decent. Not only that, but it will only spawn the blocks if you are on the ground, jumping or flying will not cause the bridge to reform until you touch solid ground, unless you hold shift forcing it to form under you. And for one final convenience touch, right clicking a phantom block with a block replaces it with that block, making roofs and platforms far easier to build. That should be enough for one night, I'll dub this sigil �The Phantom Bridge� and call it a night.

//TITLE Sigil of holding
//ITEM AWWayofTime:sigilOfHolding
Vlad asked me to help him today, requesting my assistance learning the sigils. Part way through he asked if it was ever burdensome having so many on me at once... And I told him it was. Now with a little of his help I've created the sigil of holding, and it... Well, it holds other sigils. Shift right clicking with  it while another sigil is in your inventory will cause it to absorb that sigil, taking all of it's powers and allowing you to swap between them with a shift right click. Unfortunately, it can't hold that many sigils(Only four) and not every sigil wants be become a part of it. Also, I had to ask Master Magus for help removing sigils from it as it refused to let them go. For this task he created the ritual of unbinding, a rather grand looking ritual for such a simple task I must say.

//TITLE Elemental affinity
//ITEM AWWayofTime:sigilOfElementalAffinity
Demir hurt himself again today with a fire spell that backfired(Well, it worked perfectly if setting the ground around him on fire was the intention). In hopes of stopping further injuries, I have infused the powers of the water, lava, and air sigil into an imbued slate. This seems let the sigil act as protective aura around the user, shielding them from drowning, burning, or falling damage at a high over time LP cost. Since I asked Demir to use it he hasn't burned or bruised himself, but did pass out once from a lack of LP to fuel it. Sometimes solving one problem only makes more... But he has been learning, and I've been amazed at what his spells could do. Recently he made a long range teleportation spell and a flight spell not unlike the air sigil, but far faster.

//TITLE Recreating ritual stones
//CRAFTING AWWayofTime:ritualStone
//CRAFTING AWWayofTime:masterStone
After a few days of nothing, I turned my attention to the construction of Master Magus's ritual stones. Looking them over, I might be able to replicate their usage and features using reinforced slates and obsidian... 

Master stone, more complicated then the average ritual stone might be possible if I use my magician�s blood orb and four ritual stones to handle the complex nature of it, along with a little more obsidian... Yes, yes it should be possible to recreate them without sacrificing any usage of them. After asking him how he originally made the elemental scribing tools, I believe I can also replicate the manufacturing of them in the blood altar... But it looks like the dusk inks are too complex for this altar to handle, creating an ink closer to dark gray then midnight black... Perhaps I'll have the power to form them later, for now these four will work.

//TITLE Shining a Blood Lamp
//ITEM AWWayofTime:itemBloodLightSigil
I had a flash of inspiration today (Quite literally, as watching a few town guards light a replacement torch nearly blinded me from the sudden light). They spend so much on coal and charcoal to fuel those things... What if I had a simple replacement? A near invisible light source that never goes out, can be made almost endlessly, and can easily be carried on your person? Never again would you need to place a torch to light a room, and they can ruin a rooms look(Not to mention the fire hazard they pose, ruining the look even more. �Charred mess� is never in style.).

Harnessing the power of glowstone by running a �Current� of LP through it, I can create a sigil that fires a projectile that upon landing forms a stable light source par with that of torch. The only sign of its existence is the light it gives off and faint red particles. While it uses a small amount of LP, the advantage over carrying stacks of torches is enough to justify it.

//TITLE Bound armor
Today Magus gave us a lecture on armors, going over the practical applications(Protection from large pointy sticks shoved in your direction and holding magic to improve the abilities of the wearer), typical construction of armor, and notable armors through the ages (And the very fatal flaws that their owners eventually discovered, much to their short lived dismay). Seeing and reading all this, I feel it is time I take my own try at this art using blood magic. Testing a wide array of materials and structures, I discovered a process to make an almost indestructible metal using a casing filled with life essence with glass hardened with diamonds and reinforced with bright red blood shards(More on their properties later) Magus showed me a while back (Making the prototypes of this armor took longer then you would believe). The process needs the soul of a demon bound to it, but this seems to be a small price to pay. 

//CRAFTING AWWayofTime:emptySocket
//CRAFTING AWWayofTime:armourForge

To start, you need to make a �Soul armor forge�. This block summons the demon soul and controls the transformation of the �Socket� blocks into proper armor. By placing the filled socket blocks around the soul armor forge in roughly the shape of the armor piece you want with the forge in a gap, you set what it makes. Right clicking the forge will then finish the process, creating the armor piece and binding the demon to the shards inside the armor. You will need to bind the armor to yourself, allowing the armor to drain your network to repair any damage it takes(Another benefit of using blood shards in the creation). After donning the armor(Making a few adjustments, still refining the creation process), I asked Magus to help test just how resistant this armor really is. Several nasty bruises later, we have decided this armor shields the wearer from ninety percent of damage from mundane damage sources. What is interesting is that it also blocks roughly eighty percent of magical damage, meaning many armor piercing methods are far weaker. The metal also doesn't seem to hold enchantments, and the demons inside reacts quite negatively to our attempts to do so.

Using the ritual of unbinding, we are able to deconstruct the armor back into the component sockets and anything that was inside them. I mention this because we seem to be able to �Augment� the armor before creation by adding a combination of sigils, blood shard, and an orb. The sigil added to the armor piece(You can add anything to the socket by right clicking with the item in hand) determines the effect, and adds a passive cost to wearing the armor piece. The blood shard lets you add an upgrade, the weak shards we have at our disposal only allow one upgrade but in theory a stronger shard should allow more to be added. Finally, the orb seems to lower the cost of the upgrade. Every sigil seems to have something to do with the normal effect of the sigil, either giving you a passive bonus or having the effect of the sigil happen automatically around you. A few examples are a water sigil making you immune to drowning, the fast miner always active, and a green grove sigil acts around you.

//TITLE Sanguine armor
//ITEM AWWayofTime:sanguineRobe
I've managed to use my new found knowledge of armors and experience making bound armor to try and improve thaumium armor using blood magic. By throwing goggles of reviling or thumium chestplate, leggings, or boots the ritual transform the armor into a �Sanguine� version, with a small vis discount and protection par that of pure diamond. What is really interesting is that this armor can be augmented with runic shielding, and then socketed into bound armor in the same fashion as a sigil to pass on the runic shielding, with the bonus that adding the sanguine helm to the bound helm well allow the wearer to see things like aura nodes as if you had goggles on. I should note that you can only socket a sanguine armor piece into a same armor type(Helm in the helm, boots in the boots...), and that any vis discount is lost when you do this.
				
//TITLE Suppressing the soul
//ITEM AWWayofTime:armourInhibitor
Having the armors special effects all the time has become a little bothersome. To deal with this I have managed to create an �Armor Inhibitor� to repress the special effects it offers in the cases they are more bothersome then helpful. Unfortunately, it is a bit all or nothing in this regard. 

//TITLE The ritual diviner
//ITEM AWWayofTime:itemRitualDiviner
It's coming up to the three year anniversary of Magus taking my on as an apprentice. Time seems to have flown by while I've been engrossed in research on applications of blood magic and working with Magus... I should make a gift for Magus to as a thank you for these last few years. Considering the complex nature of his rituals, an aid to building them might just be the perfect gift...

Ugh... Making a way to build rituals easier proved to be quite the task(And it reminds me of the rule of conservation of effort...), but I finally have it ready in time for the anniversary. While it isn't cheap to craft(Costing the four elemental inks, four diamonds and an emerald), I've gone to pains to make it easy to use. Shift right clicking will cycle through a preset list of rituals, and shift right clicking on a block with also rotate the direction the ritual will face(For anything like the ritual of speed, where that matters). Shift left clicking will go through this list in reverse, for those times you accidentally go past the one you want. By normal right clicking on a master ritual stone the diviner will take stones out of your inventory, place then in the world and ink them to build the set ritual. To make it clear it is working, it emits green particle effects whenever it places a block successfully. However, it can only place a block in an empty space. Anything at all in the way will stop the process until the obstruction has been removed. It is a small annoyance, but nothing major enough to risk breaking the diviner(Again).

Magus adores his gift, having rituals faster and easier to build is the one thing he could actually use(That he doesn't already have). The fact it makes anyone who uses rituals lives easier is a just a bonus at this point. As it turns out, he got me a gift as well: A pair of ichorium wand caps, the one thing I had been dreaming about for a long time...

//TITLE Blood shards
//ITEM AWWayofTime:weakBloodShard
Reading through my older entries for inspiration on things to do with our current power(I believe we have started reaching the limit, struggling to stretch what we have as far as it will go) I noticed that I �Might� have forgotten about covering the blood shards like I said I would a few months ago... I'd best start from the top.

Blood shards, as Magus has likely stated already, are fragments left behind after vanquishing a hostile mob with a demonic weapon(The Bound sword or Energy Blaster). Upon close inspection, I see small strands that looks shockingly like that of our soul networks... Well, in theory anything with a �Soul� could have a network, but this is a surprise nonetheless. Because of this, the small shards contain a vast pool of energy that our magics should be able to tap into. A rather unfortunate first experiment involving stone showed that this energy can be wielded to create or transform matter(I believe several others have come across similar principles to convert energy into matter using science). I say unfortunate because I was stuck inside the lab's supply closet for a few hours to hide from the rapidly expanding stone as the released energy of the shard grew the stone to thirty two times it's former size, filling the rooms and pushing out the open door and into the hallway. (Further crafting of blood stone was done outside after that...) But regardless, the blood shards potential energy and connection to the soul network is how we manged to form bound armor so easily.

Thinking about it... Blood shards might be the answer to our power problems. The last time we needed an upgrade, we used something related to the altar... Perhaps using something related to the soul network is the next step? 

//TITLE The life of a Mage
Once more I must make a progress update, as daily life as taken up most of my time these last few weeks. Magus has started offloading some of his work onto us, dealing with small problems and formal matters, bandits and demons, and general mage work. I have been able to do a little work on the altar or blood magic in general over the last few months because of this. Thankfully, we've burned though most of the grunt work that has piled up and I now have some time to conduct our research. Magus and I have spent the last week doing the calculations for upgrading the altar, determining that bloodstone should work for the capstone, and now it is just a matter of seeing how many runes we can get away with adding without nasty side effects or it all just blowing up(The other three have placed bets on this, I believe). 

//IMAGE 8 854 480 alchemicalwizardry:textures/misc/screenshots/altars/T4.png

After another few weeks of testing, balancing, and error we have finished the altar upgrade. This new tier adds twenty eight more runes to the altar's structure(Seven on each side, for a grand total of fifty six), and four pillars capped with large bloodstone brick(This was the best material we could find that doesn't expire or fall apart. Or, in one memorable case, actively try to kill us). With these additional runes we are projected to be making roughly one thousand three hundred LP for every heart sacrificed, a hefty improvement from our seven hundred from before. Unfortunately more work has come in for us, so once we have the altar built it will be some time before we can focus on the hunt for the material for the orb. Current plan is to start with precious materials like before, and work from there. Ah, innovation... Ten percent work, fifteen percent knowledge, five percent patience, seventy percent trying random things to see what happens.

More work then expected flooded in at once, and we have been struggling to keep up with it all. Vlad has been acting as a healer and doctor trying to deal with a plague that has going around (Caused by a witch-in-training using the wrong vial in a love potion), and has managed to keep it from running wild. Demir has been clearing out known bandit camps, and has enjoyed a chance to test out the spells we've helped him build(Only in the mage guild are bandits considered an endangered species. This is in part due to the average intelligence level of said bandits, and thinking that an area where there are people who, sometimes quite literally, shoot fire out of their hands, mouths, and other places(Mostly the apprentice mages, as a joke or party trick that has often gone very wrong) is a great place to set up shop.). Bella meanwhile has been dealing with the demons that have been wondering in, the mage guild  acting as a sort of beacon to them due to the thinness of the fabric of space here caused by countless magical experiments, both successful and not. Magus and I have been going through a huge pile of letters(Seriously, where are they all coming from, and how do I not notice the pile building up until it flows off the table?) from local villages and mages, ranging from simple questions about an issue or problem to requests for assistance in matters ranging from theft to flooding. We've been coordinating these issues, mapping out journeys to deal with as many as possible on one trip. 

As a reference point for how long it has taken us to deal with it all, dust has gathered on this book since the last time I wrote. Thankfully we've sorted out the letters and referenced the villagers to mages who are closer to them(Much to said mages chagrin), so it should be a while until this is an issue again... Thankfully all this time has left me with a few ideas for future projects.

//TITLE The masters of T4 altars
//ITEM AWWayofTime:masterBloodOrb
The others have tried countless gems and metals trying to create a new blood orb, all of them either did nothing or created a flawed orb(Cracked or chipped, extremely fragile or incomplete). But last night I had an idea from a dream... The weak blood shards were once part of a soul network, so what would happen if we tried to reforge it into a whole orb? It is better then any other idea we've had, so I might as well try...

Success! While we did burn through several instant health flasks, we now have half a dozen new blood orbs. While we haven't named them yet, they are a true master piece... A Master blood orb, that name has a nice ring to it... I'm getting side tracked again, but I must remember to suggest this name to the others(It at least is better then the temporary name of �Shiny yellow blood orb�). With this new found power, I might be able to bring to life some of the ideas that have been just out of reach up to this point.

The first interesting property of these new orbs has been shown in our bound armor. While lesser orbs simply lower the cost, this orb is able to negate it entirely! A simple upgrade, but a very useful one... 

Another altar tier, another slate tier. Dubbed �Demonic� after the insignia that has formed on it's surface, we should be able to put this to some good use... I've also noticed that a demonic slate can be used with a weak blood shard and master orb to �Grow� the shard making four more.

Going over my earlier successful experiments, I might be able to tweak a few concepts used to achieve a new result. I'm starting with the fast miner, and if boosting my arms was this useful then what would happen if I augment my legs? Using cookies, sugar, and some of the normal materials I have created a new sigil. Time for a test run... Two hours(And two very sore legs) later, I've concluded the tests. The �Sigil of Haste� boosts the users leg muscles, allowing you to both run faster and jump higher then normal. The best part? It seems to stack with the effect of jump boost and speed(Which Vlad was kind enough to brew a potion for testing). Now then, I need a rest after all this...

//TITLE The sigil of whirlwinds
//ITEM AWWayofTime:sigilOfWind
Continuing with my thought process of �Reworking the old�, I have used a master orb, demonic slate, two ghast tears, and feathers to reform an air sigil. The �Whirlwind sigil� changes the core property of the air sigil to have the winds not effect the user by propelling them, but instead creates a bubble of wind around them pushing away many forms of projectiles. I should note that not all forms of projectiles can be pushed back by the winds, and it has a tendency to effect your own shots. It isn't perfect, but it is useful enough to have around in a pinch.

//TITLE The sigil of compression 
//ITEM AWWayofTime:itemCompressionSigil
After hearing the local miners complain about always having to stop work because they've run out of space in their packs, I've built �The Sigil of �Compression�. While active, the sigil will check your inventory for anything that can be compressed into a �Storage� block(A two by two or three by three recipe of that item that can then be crafted back into the item. An example is redstone into redstone block.) and squeezes it together for you. Simple, but extremely effective...  I should note I added a few special cases to the sigil, such as glowstone dust to glowstone and a restriction to only compress cobblestone if you have more then a stack.

//TITLE The Ender severance 
Bloody endermen... Magus sent me out to try and stock up on ender pearls, but the pests keep warping away from me only to pop up later(And almost always when I don't want them too). It is just after dawn, and I've only managed to bring back six pearls... But maybe that is enough. I recall seeing some research notes on endermen and how they are able to teleport somewhere in the library. It is only an idea, but it might be perfect for this task...

Eureka! After reading through the notes, I worked out how endermen connect to The End as a power source for their teleportation. Using a few ender pearls and eyes of ender, I've completed a sigil that acts as a sort of �Signal blocker�. In layman's terms, it disrupts the connection to The End and renders them incapable of any teleportation. Not only that, but it also prevents many forms of magic that relay in similar methods, such as Demir's spells and the barbaric(But nevertheless effective) act of throwing an ender pearl. From the light, I'd say it is almost sunset. Time for some well deserved payback...

//TITLE The Teleposer
After my research into the basics of teleportation, a thought struck me: As it stands we don't have an established transportation network for mages to use, instead using the long and winding roads or other means of land travel. Sure, a witch might have a few way stones or a broom and a spell caster has a convenient recall spell, but we don't have a dedicated transportation system. Considering how much Magus and I travel to deal with small matters, and how often mages visit each other it is a surprise no-one has built something like this sooner! 

After talking with Magus about this, it turns out something like this has been tried before. The main failing point each time was the limited number of people able to use the method established. (A thaumaturge rarely knows how a waystone works and so on.) This has prompted me to create a means of teleportation that, once properly set up, almost anyone can use if they know how to push a button. Further research into ender pearls is required...

After close analysis, I've decided that my first priority should be the improvement of the base ender pearl. As it stands, they are a little too fragile and random for reliable long term transportation. By placing a pearl inside a T4 altar I have been able to refine it into a focus, stronger and more predictable then the base pearl. I am currently in the middle of creating a mechanism to hold and operate the more complicated bits of the process. My materials for this is mainly gold(A metal that hasn't shown any side effects to the ender energies used), two ender pearls(One input, one output), and the focus itself to handle the conversion. 

I've managed to finish building two of the �Transportation of matter across two relative positions devices�, or teleposers for short. How they work is simple(-ish): To perform a teleport you need two teleporsers. Next, bind a teleposistion focus to one of them by right clicking on the teleposer, bind it to yourself by right clicking on anything else, then slot it into the second teleposer and apply a redstone signal. If the focus is bound to teleposer A and placed in B, then teleposer B is triggered with redstone, everything above A will move to B, and everything above B will move to A(I hope this is clear enough description, it is hard to put into word the process. If you try it yourself, it should be clearer.). This means that if you want a two way transport, you can have two foci(One in each teleposer) and trigger the one you stand on, one have one focus and always trigger the same teleposer. In theory, we could build a �Transport Nexus� by having an array of teleposers bound to the same one(One A, multiple B's, triggering the B's to do the transport).

What is great about these teleposers is that they don't just transport people, but any mobs or blocks in range as well. It even functions on complex blocks like furnaces and chests! With a little redstone logic, amazing things should be possible with this simple(-ish) system! With the completion of the final prototype, I should get some sleep...

A new day, a new idea! The basic teleposition focus only transports a one meter cubed area above it, but if I improve the focus a bit in the altar it will be able to handle a three meter area! Not only that, but it can be further boosted by adding a weak blood shard so it works in a five meter area, then these new demonic shards that Bella has been gathering for a final upgrade to seven by seven by seven area. I should mention that the cost of each transport is based on how much is moved, and how far it is moved. It also has the quirk of being unable to transport people or mobs across dimentions.

//TITLE Sigil of suppression
After a recent flood where Magus built a ritual right in the middle of the village to save a huge chunk of it, I've decided to try and replicate the effect on a smaller, and much more portable, scale. Thus, I've made the first version of the �Sigil of Suppression�. What it does, in layman terms(You don't wont me to get into the back end details involving temporary storage in a pocket dimension) is create a bubble around you that removes any liquids, replacing it all as you leave. Using this sigil, I was able to take a leisurely stroll under the nearby lake. I am glad I remembered to bring my elemental affinity sigil however, as the early prototype fail on the way back, so I had a very soggy trip home... I �Think� I found the cause of the issue, and have patched it.

//TITLE Superior capacity rune
Taking a break from the sigils I've been making over the last few months(How time flies...), I have shifted my attention to improving the runes we have available for our altar. After going over the design of the capacity rune, I have found room for �improvement�. I have that in quotation marks because it only becomes greater then normal capacity runes if you have more then fourteen of them. You see, the �Rune of superior capacity� adds ten percent to the altars capacity, and this effect stacks multiplicatively(Layman terms: The more of the rune you have, the stronger the next rune will be) instead of the normal capacity runes static rate. Powerful, if a niche rune.

//TITLE  The rune of the orb
Considering the amount of LP we burn through on a daily basis running our sigils, powering our spells, fueling the rituals, and feeding our tools and armor, it is a miracle we don't run out of LP more often then we do. Even with our soul network topped off with a master orb, we're tapped dry within a week. Just upgrading our orbs has served us so far, but that has shown to be too unreliable and a huge investment with the altar... Wait, we're always using our orbs to make runes to improve the altar, so why can't we make runes that let the altar improve the orb? It can't be a one way road, can it? Yes, if we tweak this here and shift pressure here... I'm rambling again, I must talk with the others about this!

Yes, it works! By focusing the power of three blood orbs and using two demonic slates, we've created �The Rune of the Orb�. This boost the altars ability to feed an orb(Think of it like pressurizing air, more of it in the same amount of space), letting it add an additional two percent per rune(Additive rate, so more runes won't change how much it adds). With our master orbs, one rune is an additional twenty thousand LP. While that doesn't sound like much(To think, I use to be limited to only five thousand...), consider the number of orb runes one altar can have...

//TITLE A field trip
After hearing the demands of the younger apprentices, we are leaving tomorrow for a quick tour of the magelands, and expect to be back by the end of this month. Talking with Magus, I agree that we've been pushing ourselves hard lately with work and research. A little rest during a vacation might be just the thing I need to gather ideas for future projects... It will also give me a chance to talk with other mages about setting up a teleposer network. It is a vacation, so a little work on side projects you normally don't have time for is normal right?

Talking with Magus during the long hours on the road, a few details about the mage guild were covered on this horseback lecture. Largest of the �Nations�, most of the tech focused nations won't touch this area due to the high levels of magic in the land, air, and water causing technology to become a bit more unpredictable(Read: Unstable and often highly explosive), and have instead left it in control of the mages. As a result of this, thousands of kilometers of land filled with river valleys, harsh deserts, frost and snow covered forests, fertile plains and countless other biomes have been untouched for us to expand into. To this day there are people who venture out into these untamed lands hunting fame and fortune clawing over mountains and through the ancient ruins that litter the area. However, the vast majority of settlements are close to the borders with the other nations, thriving off trade(Their gadgets might not function here, but the results are another mater. Most of the mining towns send out the harvested ores across the borders for processing). 

The last tally was done roughly five years ago, with the number of total settlements at around three hundred scattered thin across the mage guild. Of this, we have roughly seventy active mages and thirty classified as �Apprentices� to manage village needs and deal with problems as they arise. Generally one mage would managed half a dozen of the closest villages and any apprentices they take on comes from this area however, it was not uncommon for mages to offload work onto other local mages if they have not the time or expertise in the field and for them to take on students who seek them out as tutor.  

That's enough for tonight, my watch is almost over and I feel sleep calling me. I'll write down any more interesting details I learn tomorrow.

Magus said we should reach another village tomorrow, and he knows an inn there that should take us in for the night(Just in time as well, I don't trust those clouds on the horizon). Today�s trip was more of the same, ridding through a dense oak forest while Magus went over anything and everything about the area around us and the Mage guild in general. Mages seems to have a list of unwritten rules governing how to act, what to do, what to say... For example, it is recommend to live at least a mile and a half from the closest village. Far enough away that people won't bother you with trivial things and you keep a mysterious reputation with the people, but close enough to be convenient for buying what you need. Another example pertains to marriage: A mage is free to wed whoever they like(Even other mages), but the spouse is entitled to most decisions and any compromises favor the spouse. Being wed to a mage often causes problems for the spouse, often with relatives and friends, and is typically a huge adjustment. Doing anything to try and make it up to them is common, as the mage feels themselves a burden and cause for their loved one's strife. From what I've gathered and how Magus speaks about the topic, these are more general outcomes and human reaction to the same events that Magus has seen happen time and again instead of rules or traditions passed down from master to student. One final interesting fact is that most mages send their children to learn the art(If the child so chooses to take on magic, and the have the talent for it) with another mage, preferably one they have rarely or never met as to prevent any bias and favor.

I was right about those clouds, and we have reached the small town of Salis soaked to the bone. We're all currently sitting by a fire in the Mundes inn letting our robes dry and bodies warm from the chilling rain(From the clinking outside, now hail). I plan on acquiring another books tomorrow, and writing any more facts I learn from this trip with Magus in it instead of spending the pages of this book(Already longer then I ever dreamed it would be). According to Magus, we should be able to meet another mage in two days time, a woman by the name of Athis who is skilled in botany and herbs(Any medicine made by her always trades for a fair few coins). On another note, what Magus said about about people being wary of mages is holding true. The common room has been divided into two parts since we got here: The side where we are, and everyone else who have gone a table or two away from us and keep glancing at us as if to make sure we don't do anything without giving them a chance to run. The owners of the inn, a small family known as the Eldritches, seem to be the only ones who don't seem to mind us. Their daughter, or at least someone the right age and look to be their daughter, happily walks up to us asking if we needed anything other then rooms while we rested. Vlad and Demir order food for us all, Magus inquired to the drinks available, while I have asked if she knew if the general store had any writing journals for sale. And now I'm at the point of writing things as they happen... I needs some sleep in a nice bed after all this riding(But it looks like Demir is more saddle sore then I am), and the inn keepers have giving us cheap logging in the attic with a few decent, if old mattresses in a warm room with a roof over our heads.

We left the next morning after a short trip to the market(Where I managed to get a good deal on a fairly nice journal, and have already filled out a few pages on my watch last night), and now are almost at Athis's residence. We currently are taking a short rest to eat and have the horses cool off after a long trip before riding the last leg of the journey after dusk has set. I'll detail anything interesting that happens.

Talking with Athis, who I have already detailed in the other book along with most of our conversation, I managed to steer the conversation to how often she needs to travel for herbs, and if she has ever been in a situation where she can't get access to anything she needs but another mage might have an abundance of. As a response, she handed me a list of everything she buys when ever possible because of how rare it is here and the versatility or uses of it. The list was ten centimeters thick. Seeing a chance to get another mage's opinion, I mentioned a recent discovery in the field of magical transportation see might be interested in seeing. At this point, she gave me a wary eye and said she still has a bag of purple chalk and a stack of waystones from the last person who said that. Inviting her outside for a demonstration using a few of the teleposers I had packed, I showed step by step the process and how simple it was to use.(It took her a moment to recover from the initial shock of teleporting, but now she takes it in her stride.) Going over a few more details inside with hot tea, I asked if she would be interested in us setting up a link in the area nearby for her to use as a means of transport. Frankly, I was nerves enough at this point that I thought see would just laugh at my new goal. Instead, she was almost begging me to set a few up around a few locations several hundred kilometers apart. At this point I felt comfortable to tell her about my grand plan for setting up one of these near every mage in the guild and at some important points, so the days of needing to travel day and night to reach another mage would be over. Not only was she on board with the idea, she even offered to help us set them up if it meant that it would be ready faster! We spent the next few hours talking about this late into the night, covering little details and about enlisting the help of other mages to set it up.

//TITLE The key of binding 
Well, this has been an interesting trip... We've been visiting every mage we pass, talking and showing off the boons of our blood magic. Most of them have been extremely accepting of our arts, and the ones that haven't been at least aren't opposed enough to get violent about the subject(However, all of them are still welcome to the idea of a transport network). We have noticed one problem on our trip, however: We can't easily give people the things we've been working on, as they need LP to run it and it takes time to reach a point of being able to support the usage of them. We've gotten around this somewhat by binding the items we gave them to ourselves, but anything they make once we've left will be near worthless to them� THAT'S IT! I could make a device that mimics the binding of an item, copying a thread of our soul network to another device, so anyone can craft something new and have us power it. I can use a weak blood shard to hold the soul network thread that it uses to pass on the link... Yes, I should be able to make this easily.

I really should have slept after getting home, instead of going straight to building something... It took me three hours to think of using gold for the �Key of Binding�. Regardless, it has been finished. All you need to do is right click with it to set the original owner, then hand it off to the person you wish to use it. Then they right click with it, and it will bind the first thing it finds in their inventory without an owner to the person the key belongs to. While it's uses are limited, it is something you want to have around in case you ever need it. 

//TITLE The trials of a T5 altar
In other news, we've started talking about what we do now that we've started reaching the limit of our power once again. I've calculated the number of runes we would need, and have in fact already crafted all fifty two of them. We even have a few guesses on what to use for a blood orb, starting with these demonic blood shards... The problem now is the stabilizers we need. There is only one thing I've found that can handle the strain of the altar: The raw power of beacons. We need four bloody beacons. One is near impossible, four is... We need a miracle to get any farther. Magus says he has some plan, but I think even he is going to struggle with this. Talking with the others, I believe I should focus on setting up �The Nexus� instead of struggling with the limits of our master orbs. This is going to be a project that will take months... I've decided to limit the usage of the Nexus to mages, as public usage would tax my network night and day. I start tomorrow on this task, and plan on cleaning out some ruins about a kilometer to the east of us to set up the Nexus base. I already have a few dozen teleposition foci crafted, and enough teleposers to set up over a dozen locations. First on my list, Athis... I'll leave this book here, and will document anything that happens in my other book. Once I start research in blood magic, I will write in here again.

//TITLE The price of power
Magus... Magus has gotten us a reservation to kill Withers. Not only that, but we each must kill one on our own. I said I wouldn't write in here again until research started again, but I felt this is significant enough to mention... We are so close to being able to upgrade the altar. If we live through this trial, that is. 

I have written the details of the last few days in my other book(It has gotten far more use then I ever expected), but we have them. Enough nether stars to finish our altar, enough to experiment with, enough to make being stuck in bed with this broken leg worth it all.

I have been hard at work since my leg healed, ideas gushing out of me to the point I have almost stopped eating and sleeping(Thankfully, one of the others tend to drag me out occasionally). Time to document the fruits of my labor.

//TITLE Demonic orb
Our assumption about demon blood shards was right, and after a long crafting process we have the Archmage orb. We had to spend several hours trying to fill this orb, and finally hit the cap at ten million LP, ten times the size of the master orb... We also have graduated up to the slate dubbed �Ethereal�. I know this isn't much fan fair, but this has become a bit of an expected event.

//TITLE The unspeakable power of the energy bazooka
First thing I created with this new orb was an improvement to the energy blaster, as I felt it just wasn't strong enough during the fight with the wither. Harnessing the power of a few reagents and a demonic blood shard, I have created the �Energy Bazooka�(Not a clue what a bazooka is, but Demir tells me they shoot big explosions). Costing twenty thousand LP a shot, it launches one main projectile that upon hitting something explodes into twenty more smaller explosive projectiles to devastate other living things around it. I have tried tweaking it so that terrain isn't harmed, but there may be minor damage depending on what is hit. There is a �Slight� problem of recoil, but nothing we can't live with.

//TITLE Acceleration runes
Moving my attention on to runes for our wonderful new altar, I had revelation as I noticed a speed rune next to a dislocation rune: Increasing how much LP is moved from the buffer is fine, but how about increasing how often it transfers LP? Using a few materials like buckets and an ethereal slate, I have transformed a normal speed rune into something far greater. Instead of effecting crafting speed, one rune will lower the delay the altar has on moving the contents of the buffer by a twentieth of a second per rune. I have noticed a strange limitation, however: Any runes beyond nineteen seem to have so little effect it isn't noticeable anymore, so you can only have the altar transfer nineteen times a second. I will need to run some experimentation on the best ratios of acceleration to dislocation runes...
//TITLE The Harvest goddess
Bella has dragged me with her to town so she can be sure I'm out of the lab for a while(I have noticed I'm a little pale, but I just assumed that was from all the sacrifices I've been making lately). As we ate two sandwiches at a bench on the edge of the village, I watched the farmers in the field slaving over the harvest while trying to outrun the storm just visible in the distance. Talking with Bella about ways to help them, I created the basic idea for the sigil now known as the �Harvest Goddess�. It is to the �Reap of the Harvest Moon� what the �Sigil of the Green grove� is to the ritual of the green grove, performing a similar effect to the harvest moon around the user by summoning a little demonic imp(You can thank Bella for this part). As soon as we had a version ready, Bella ran out into the field with a green grove, haste, and magnetism sigil to quickly run through the field collecting the harvest behind her. Needless to say, the few farmers in the field were awe struck by this sight and a few of the newer ones thought she was a goddess sent to answer their prayers(And I liked the sound of it, so we now have a name for the sigil).

//TITLE Solving a demon problem
A mage can never get a good rest without something happening... Magus has finally launched an operation to deal with the demon portal, and has asked Vlad, Demir, and myself to lead the siege team. He has cooked up some complicated plan that only he knows all the details, and in fact might be the only one who even has any idea if it might work(Quite a few of us felt lost when he gave the basic overview of the plan). One question asked at the meeting that felt quite memorable was �Wait, how are you able to replicate this portal?� Magus only smiled and asked him �At what point does science stop being science and starts being magic? It is the same principles and theories at work, just a different means of execution.� For Magus, that seemed to answer everything. Gah, I need to focus on packing for the trip as we won't be back for a few weeks. I feel like I heading out for war, and I guess in a way we are...

Alright, I did not expect to find a camp of demons when we warped to Venric's folly. I expected a hole in the fabric of space, I expected a few demons, but a little village... Not to mention the fact that I had to save a fairly inexperienced thaumaturge who hasn't dealt with demons before. He rushed in, thinking he would show off a bit. To his credit, he did manage to kill a few demons before the hoard took him down. I had to run in with a teleposer to save him, but we did discover something important: From the vanishing remains of the demons, I retreated two crystals. One was a shiny red we have named a �Life shard�. The other a brilliant blue we have dubbed a �Soul shard�. What is odd is that my bound armor seemed to react when I touched them, and I saw a few stray strands of soul strands between the demons and these shards. But that isn't what was really odd.

I was spotted by one of the demons when I went to save the fool, and noticed two things very quickly: The demon had a connection to the portal with a few soul stands.  The second thing was that it seemed to be... Vibrating? Closest word that comes to mind when I think of it. Shortly after that, two things happened: The demons started running towards us from every part of the village, and for a few hours after we left the camp seemed to be under a lockdown. The demons had doned armor, and were patrolling the area instead of building. These guards seemed a little stronger then the others, but tomarrow I plan on mounting an operation to collect more of these shards for research...

//TITLE The T6 altar already
From what we have learned so far, these crystals seem to be ordinary crystals(Well, ordinary as anything from Tartarious can be here) filled with demonic aura and... changed by the travel through the portal. These demons have been using them as a sort of personal anchor, an extension of the main portal's influence. I have discovered that mixing these crystals together in a five to four ratio of life to soul shards forms a purple block that is practically pulsating with demonic power. Since we may be here a while, I have started construction of a prototype blood altar that utilizes these new blocks for enough stability for a T6 to be possible. I have yet to try this, but if it works... 

Once again, a theory proved to work out. With the addition of four pillars capped in these crystal clusters, we are able to support another seventy six runes, nineteen on each side of the altar. I have also tried the clusters out for making a blood orb, and the �Transcendent orb� was the result with a cap of thirty million LP.  Sadly, I haven't had a chance to experament with this new altar or orb, so I haven't anything else to share yet. 
