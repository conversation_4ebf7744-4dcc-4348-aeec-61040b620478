--Entries--
aw.entries.architect.intro=My name is <PERSON><PERSON><PERSON>. I was a kid when the demons came for my village during The Wars. They ransacked the houses and turned the shacks into splinters, wielding fire and water to blast the land asunder. I woke up to some travelling merchants that were passing by, equipping the warriors who were futily trying to drive off the demons that still clawed the village. I was brought to a village nearby, where a magician named <PERSON><PERSON> helped tend to my wounds. The magic that he used was something that I had never seen before - it wasn't Thaumaturgy, nor Alchemy, and it was definitely not Botany. He winked at me once he saw that my eyes were open, holding his finger to his lips. Fast-forward several years, and I have learned almost everything from Master <PERSON>, being his third student ever to master his arts. Against his wishes, I have recorded my research and put several wards and spells on this book. So welcome, apprentice. I am known as The Architect, and I am a Blood Mage. It took several years of pestering before I managed to convince <PERSON><PERSON> to teach me. He kept on telling me that, "Magic that uses the life essence of living beings requires patience and preparation in order to master it. One false move, go a little past your natural endurance, and you may find yourself taking a nice vacation in Tartarus." The thing was, I wanted to go there - I had some unfinished business with the demons. The process that <PERSON><PERSON> originally constructed required powerful artifacts that he constructed himself, but were rather lacking where teaching was concerned. After studying a bit of alchemy and the process of "Equivalent Exchange," I managed to construct myself an altar that would transmute items inside of its basin into new powerful forms. The only issue was that it needed a worthy catalyst, and so with a prick of the finger I set the Blood Altar alight!
aw.entries.architect.bloodAltar.1=To start any form of transmutation involving blood, you would need to construct a blood altar and a sacrificial knife, as well as have a solitary diamond in your possession. After placing the blood altar down, Magus advised me to be careful as I filled it slowly with my blood, and said that I would need to be really close to the altar (about a metre) for the knife to work. With about 2 buckets of blood in the altar, which Master Magus reminds me is about 10 hearts worth, I placed the diamond inside of the altar by activating it with the diamond in hand.
aw.entries.architect.bloodAltar.2=The blood dissipated in a cloud of red swirls as I waited for the atoms of the diamond to shift and reform. There were a few moments where the particles turned gray, which meant that the altar was empty and I had to hurry to fill it. After the diamond burst in a shower of red particles, what finally sat in the altar was a Weak Blood Orb.
aw.entries.architect.soulNetwork=One thing that I initially didn't understand was the overarching connection between the blood orb and myself. When I initially met Magus, I could see many sparkling strands branching off of him, flowing throughout his house and linking with intricate stones and runic drawings. I asked Magus about the strands, but he had no clue what I was talking about. It took three years of thorough research to finally find the answer, and when I brought my notes to him he was really impressed with what I have found. When you send power into the orb, the energy is transmitted from the strand connecting the orb and into the very soul of the person the orb is bound to. Similarly, and Magus managed to show this effect with several of his rituals, when you use something that drains energy it will drain the energy directly from the soul. The thing is that if you use an item whose owner has no energy left, the item will instead grab the requisite energy from the user of the item. Directly. From his or her life force. As such, the unit of measurement is called "Life Points," or LP. I experimented with this, and one heart equals 200 LP. I have christened this system to be the "Soul Network," and is used in all branches of Blood Magic indirectly.
aw.entries.architect.blankSlate=Magus is a master at rituals. His power in the intricate layering of stones and inks is unmatched. The problem is that these rituals are rather... static in nature. Sure, being able to call the bounty of the earth to the surface is all fine and dandy, but won't exactly protect you when you are on fire. To this end, I decided to link my soul network to powerful items that I have created. To start, I decided to transmute a piece of smooth stone in the Blood Altar with just 1kLP to create a blank slate.
aw.entries.architect.divination=The first thing I did was to arrange the blank slate with some reflective glass and my weak blood orb. Pouring my power into the configuration created a Divination Sigil, which I could link to my network and see how much power that my soul network holds. What is more, holding the sigil to the Blood Altar flooded my mind with information, giving me the knowledge about its current tier, capacity, and even how much it was holding. Happy with the sigil, I brought a fresh unbound one to Master Magus for him to use. When I took the divination sigil back in my hands and tried to use it to view his network, for some reason I could not gleam any information from him. I don't really see why this is, considering that I used this same method for other people and I saw that they had no power at all, but to gleam actually... nothing from Magus is strange.
aw.entries.architect.waterSigil.1=I've been training my body, lately. Master Magus told me that in order to extract most of my power, I have to get my mind, soul, and body into as perfect shape as possible. Even though my mind takes time to hone, and my soul requires methods I have yet mastered to strengthen, my body is something I can actively manage. No more cookies for me, apparently, since they have horrible saturation for the work I do. And it's hard to grow cocoa right now, anyways. The temperature is ridiculously high, and with it the humidity is pretty freaking low. Some bright spark of a thaumaturge got it into their head that it would be good to open up the Obelisk to the far north of the village, and because of it a large influx of taint spewed forth from the eldritch tomb. It looks like someone wasn't focusing enough on their sanity training! Thankfully Master Magus decided to head out to the site up north with a few stones and a new vat of ink, saying over his shoulder that he had a new ritual that he'd like to test to fix the problem.
aw.entries.architect.waterSigil.2=In the meantime, I was tasked to create something to fix our water problem. I thought about perhaps cooling the environment around the village in order to condense the water in the air, or perhaps find a way to create rain - neither of which I would be able to accomplish, because I'm sure that either task would take more than the 5 kLP capacity I currently have with my blood orb. So, I had to think small. At my desk, I mixed some dirt into the spare water I had for experimentation and started to paint on one of the blank slates I recently created to layer a bit of a base. Once I was satisfied, I took a bunch of buckets (to be safe in case that what I did would fail) and arranged them around the damp slate and then took my orb from the chest nearby. I then tapped the orb onto the slate, and the water started to boil and swirl out of the surrounding buckets, rushing into the slate! After a quick, bright flash, the new sigil lay steaming on the table in a puddle of water. Thankfully the sigil was what I needed. I could link it to my network and place water wherever I wanted by simply tapping it on the ground. The water was pricy, requiring a full 100 LP for each bucket's worth that I created in the world, but for an infinite supply of water, creeping upwards to 50 full buckets for my puny network was something that I desperately needed. It's just a shame that it started to rain an hour after I created the sigil.
aw.entries.architect.lavaCrystal=There was an explosion in the lab today as I was trying to work out a few more artifacts. When I tried to infuse a few buckets of lava with my power, and used some glass to contain the effect, the assembly simply burst into flames and flew me across the workshop. I wasn't really hurt, and thankfully my water sigil was used promptly to put out the fires, however it seemed that my experiment failed rather heartily. Apparently I needed just a little bit more time, however, since I got it to work a week after. By combining some glass and obsidian together with a few buckets of lava, and throwing in my blood orb and a diamond to hold it all together, the diamond was transmuted into a fiery crystal that seemed to... do nothing at all. I peeked out from behind the blast shield, since by then I learned that playing with lava was not the safest of hobbies, and gingerly linked the crystal to my network. As soon as the pact was completed, I felt a wave of heat emanate from the crystal, and promptly tossed it into a furnace before it could burn my fingers. I was unsuccessful on that front. The furnace immediately kindled, and the pork that was sitting in the top slot began to cook quickly. Quickly feeling a wave of nausea, I checked my network out and found that I had nothing left to offer. Once I pulled the blasted thing out of the furnace and stuffed it into a chest, the nausea subsided. So this Lava Crystal could work in any sort of furnace and feed off of the owner's network, but if the network was dry it would instead cause the owner to feel a hearty blast of nausea unless removed from the solid fuel-burning machine. Good to know, since now I know of a way to trade my services with the blacksmiths in the village.
aw.entries.architect.hellHarvest=Finally, the day that I have been waiting for the past 7 months has come. The "Hell's Harvest Festival" is a day that the village celebrates the creation of the first portal to the Nether a century ago. It's funny that when I ask the villagers why it is that we celebrate the formation of a portal to Hell, they simply laugh and say that I'll see when I finally go in. The portal was located in a vast stone cathedral, a three day's walk from the village on foot. Magus said that he'd meet us there, because he had a few things that he needed to prepare before actually going over to the Nether, but I was curious as to how he'd make it there in time - knowing Master Magus, he'd spend a day or two reading up on a new theory for a ritual and forget himself in his works. When we entered the hall, however, Master Magus was there already talking to one of the guards of the unlit portal, stowing away a gem into his pocket. I knew something was up, because I could see a strand connecting the gem in his pocket to both himself and one that trailed off back to the village. Not wanting to be distracted, I pulled out my wand (Master Magus insists on all of his apprentices learning of another field of magic, and Thaumaturgy has proved itself useful) and set alight the portal as was the custom of the newest member to the harvest. It seems that the harvest, however, was not exactly a trade. I saw giant stone walls and vast pumps throughout the land, grabbing all of the lava from the great pool below. A large collection of bound pigmen walked by, led away by someone in a full set of shining steel armour. Huge machines that processed the lava were emblazoned by the brands of industrialization: Thermal Expansion, BuildCraft, Greg Tech, and many other labels dotted throughout the base of operations. So much for a festival - it seemed that the Conglomeration, the governing body that oversaw all of the villages in the land, saw it fit to turn a civilization into a circus.
aw.entries.architect.lavaSigil=After accepting a few supplies from the farce, I walked back home by myself two days early. There were some things that I wanted to create, but wasting my time there would have been fruitless. Clearing my desk of debris charts of alchemical constructs, I set to work with a vat of magma cream that I brought with me, attempting to combine it with another slate. Casting my orb aside, I fished the lava crystal from my chest and combined it with the cream and some fresh buckets of lava - thankfully not from the Nether. Once the light faded, I picked up the new Lava Sigil that lay in front of me. What this sigil does is it siphons energy and uses the properties of the lava crystal to super heat rock, creating a source of lava wherever the wielder touches the ground. So, infinite lava, and infinite fire hazards. Unfortunately, it takes about 1 kLP per single use, which is a lot for my network as it stands currently. It looks like I need more power, but seeing that Master Magus is currently enjoying himself at the festival, I'm going to have to find a way to boost the blood altar myself.
aw.entries.architect.blankRunes.1=Of course! Runes! Why didn't I think of this earlier? The power of the altar itself comes from the structure of the components used to create it. The force of the diamond alone is not enough to create stronger materials through transmutation. What I need to do is construct several runes to extend the altar, and thus the altar's power. The only thing that I could do on such short notice is to take several blank slates (I have about 16 left) and quite a bit more stone and combine them with my Weak Blood orb.
aw.entries.architect.blankRunes.2=What came next was the hard part. After I made 8 runes for myself, I sat puzzling over the altar. "How could I extend this thing?" I asked myself. It took a few hours, but when I placed the 8 runes in the ground around the altar in a ring, I felt a strange reverberation in the air. Something felt like it awoke, as if the power in the surrounding environment doubled. Quickly, I grabbed my Divination Sigil and slapped it onto the altar, and focused into it to view the power of the altar. I was pleased to find out that the altar was finally at its second tier, which should open up many more transmutation recipes. The problem now, as I sank slowly into my bed to sleep after the long day, was to find out what I could now do. Although Magus was pleased with the upgraded altar, I was caught in a bind. I couldn't seem to do anything with it that the previous altar could not do. The first thing I did was I placed one of my leftover blank slates into the basin, and it promptly consumed 2 kLP worth of essence as it reinforced the structure. Although this Reinforced Slate was much more durable, this process appeared to make the slate no longer work with creating both the lava and water sigils, as well as making it a waste to use in additional rune creation.
aw.entries.architect.speedRunes=I can note, however, that when I combined one of the runes with a few more blank slates and some leftover sugar from the harvest it seemed to augment the altar even further. The sugar had to be laid in a configuration that mimicked a bow and arrow on top of the rune, and by doing so it created what I can dub a Speed Rune. What this rune appeared to do was increase the speed of the altar's processes, both the transmutation rate as well as the speed that a bound orb will consume the essence from the altar, by an additive 20% per rune. This tier doesn't seem perfect, however, since it seems that for the second tier only the four edge runes (not the corners) could be used as upgrade "slots" for the purpose of augmentation of the altar.
aw.entries.architect.apprenticeOrb=The "eureka" moment came when I accidentally dropped a few things on the floor of the lab. When I bent down to phish the items from the floor I noticed that one of the emeralds rolled right up to the altar, almost as if it longed to be inside of its basin. It occurred to me that if a simple diamond inside the altar created a weak blood orb, perhaps a much rarer gem would make a stronger version! Quickly fetching my knife from the table, I filled up the altar over the course of the day so that I could make sure I didn't mess anything up, and then dropped the emerald into the altar. The speed runes made sure that the transmutation took no time at all! Wow, those things work fast - I better make a note of removing the runes when I try to create more difficult items inside of the altar, because if I was not prepared it would have chewed through my supply in no time! After a few tantalizing moments waiting, the altar burst in a shower of particles and the new orb sat there, revolving on top of the altar. Master Magus must have heard my shout of exclamation, because he hurried over from his side of the house and helped me to my feet. Not noticing the pain in my palm from earlier, I reached for the orb and sealed it to my network. Master Magus then helped me to fill up the orb inside of the altar, and when we were done that day I had a total of 25 kLP inside of my network.
aw.entries.architect.voidSigil=After resting the rest of the day at the insistence of Magus, I returned to work this morning full of inspiration. While I laid in bed half awake, the thought struck me that so far I had managed to use sigils to create fluid. So why not make one that can remove it? My first attempts with things like glass bottles and bowls failed with all of them working for a brief moment, then returning to their old forms. It was almost lunch time when the thought crossed me that I used buckets for the first two, so why not here? Half an hour, six buckets, a bit of twine and a reinforced slate later I had my first void sigil.
aw.entries.architect.airSigil=While tending to our small farm a few days later, I noticed a bird land on our fence. I turned to face it as it flied off, and had a revelation as I watched how it flew. If I could use magic to create the gust of wind like that made by bird wings, I could fly through the air! After hastily finishing the days' chores, working out details in my head all the while, I rushed to my workstation to start work on what I have now dubbed the "Air Sigil". Using feathers and a Ghast tear as ink on a reinforced slate, I formed a sigil that on use creates a rush of air behind where you face to propel you forward. Flight using this sigil is fast, if a little pricey and hard to control, not like the flight provided by Magus's thaumostatic harness. Also, landing is a bit of a problem as I learned the hard way...
aw.entries.architect.sightSigil=I've grown a little annoyed at my divination sigil. While it is able to read the current tier and essence of a blood altar, that is ALL it can read. As a result, my current goal is to improve its design using my new blood orb. So far it is going well. The "Sigil of Sight" is able to read the buffers of the altar to give how much they store, as well as how far along a craft in the altar is and how fast it is using LP. The downside of this upgrade is that it loses the power to read the altar's tier or the users soul network, but so far this is an unavoidable loss. For now I'll settle with what it does now, and might try to improve it further later.
aw.entries.architect.advancedAltar=Even after all this time, I still don't fully understand the blood altar. Sure, I built the thing, but... It is almost like working directly with raw life essence has given it a bit of life of its own. After running a few experiments to try and make my life easier, I've noticed some odd behavior coming from it. First, I tried using some basic piping to extract life essence to store later to use for more pricey infusions. As it turns out, the altar doesn't like giving up its essence. I managed to get a bucket of LE out before it seemed to slow to a trickle feed, nothing more the 20LP a second. Further tests show that it has a "Buffer" tank that siphons off LP from the main tank, holding roughly 10% of the main tanks capacity. As I mentioned, it does this very slowly so piping is out of the question for now... A later test shows that it also doesn't like essence being piped in, as a similar restriction apples to imputing essence. I will defiantly need to find a way to improve this transfer rate... Another strange phenomenon about the altar is its "Hunger" for life. After the failure of the piping, I tried using a few regeneration potions from the local village to speed up the rate at which I recovered health. A normal or extended potion seemed fine, and were very effective for speeding up slate production. But a strengthened regeneration potion caused the altar to have a slight "Hum" about it, and I felt my energy being sapped by something. After talking with Magus about this, we have determined the altar senses the life flowing through us, and the potion makes the altars "Hunger" for this life create a sort of aura that causes you to lose energy akin to hunger. As a result of this, regeneration is still effective, but a sizable supply of food is needed for extended use to recover energy.
aw.entries.architect.fastMiner=I've decided to experiment with passive effects for sigils, since everything so far has been an active effect. While wondering through town looking for supplies, I noticed people cutting wood for a fire and some smashing stones to build a small fire pit. I keep coming back to how slow both of them were in my train of thought, then started going over ways to improve the tools. Then it hit me: Why improve the tools when you can improve the person? A few anatomy books and failed tests later, I hand the sigil of the Fast miner(Name pending) and two very tired arms. After activating the sigil, it speeds up muscle movements in the arms allowing you to break blocks far faster at a small LP cost every few seconds. I do need to keep reminding myself to turn it off when not in use, but the stinging pain of life essence being taken is always a quick reminder...
aw.entries.architect.soulFray=Today I pushed myself too far. I've gotten close a few times, but today was the first time I have passed out due to draining too much life essence. Magus was thankfully with me at the time, and moved me to a bed to recover. As I regained my senses, I noticed something odd about my soul network thread: They were thin and looked damaged. As I kept watching my threads, it seemed like they were slowly reforming and repairing themselves. I mentioned this to Magus, and we theorized that after coming so close to death my soul was still recovering from the shock. I made a sacrifice at the altar to test this, and it seems that this state effects how much life essence a heart is worth quite negatively with it only giving a tenth the normal amount.
aw.entries.architect.greenGrove=Today Magus showed me his green grove ritual, a way to speed up crops. Unfortunately, it is very static and has a small range, limiting its uses. To amend this, I've been toying with the concepts behind the ritual in an attempt to make it more portable. By creating an ink from oak saplings and sugarcane, then inscribing a rune on a reinforced slate I have created the sigil of the green grove. It seems a little slower and more expensive then the ritual form, but it can be carried on your person and acts like bonemeal when used on plants. While I had it active I could barely keep up with our farm, and by the end we needed to give a few of our neighbors some of the excess we didn't need. They all accepted it, but with varied degrees of reluctance.
aw.entries.architect.dagger=My work on sigils has slowed for now as I'm reaching the limits of what my little green orb can do after only two weeks. As a result, I have turned my attentions to other aspects such as runes to pass the time. Magus is currently out dealing with a large forest fire to the east of us, and the village is in a bit of a lull with nothing planed for several weeks. Right now my task is to slaughter a few chickens Magus brought home from the market to prepare on his return home, and it seems the perfect time to test an idea I've been thinking about for a while... I will not mark it here, just in case it is another false hope. Success! I've managed to perfect my idea for a new source of life essence. Before the only option was to cut ourselves to give our own life essence to the altar, but now we have a way to use other living things as a source. Unfortunately, it is a little gruesome and a bit "All or nothing" from them... Regardless, it works! ...I have realized that, in my excitement, I may have forgotten to write down what it is. I call it "The Dagger of Sacrifice", formed in a blood altar out of an iron sword and 3K LP. The dagger doesn't deal much in the way of damage. It doesn't need to, as its main purpose is to "Mark" a living thing for the altar which takes care of the rest by forcibly extracting their life essence all at once. Through my tests it seems that your common farm animal's life isn't worth nearly as much as a person, only giving 250LP regardless of where it comes from. While I set about cleaning the animals after the tests were done, a common zombie managed to sneak in the door I left open when bringing the chickens down here. As soon as I noticed it, reflexes that were being drilled in by Magus every morning kicked in and I managed to poke it with my dagger before it had a chance to hurt me. At first I thought nothing happened, then it fell to the floor as lifeless as it should have already been. I glanced at the altar, quickly noting that a zombie was worth 500LP. Looking over the fallen creature as it started crumbling, I noticed that some of the defenses Magus had set up took there toll on the zombie as it was burned and freshly cut in several areas... Seems that the amount of HP they have is irreverent to the altar, giving the same amount of LP regardless. As I finished preparing the meat to cook after disposing of the zombies ash(Which reminds me, I wanted to look up why this happens...), a very dark thought crossed my mind. The knife works by giving life to the altar. The dagger works by taking life forcibly from a target. What would happen if a human was marked by the dagger? I asked this to Magus, after showing him the dagger and telling him everything that happened. "In all honestly, I'm not sure. I have a theory, but I don't really feel like testing it..." I asked him to elaborate, so he told me the basics of it. It seemed that source of the life essence mattered with the dagger, since the chicken and zombie gave two different amounts, so depending on the person in question they would be split into one of three groups: Passive, Hostile, and Innocent. The passive would be like the chicken, only giving a little. The hostile would be the bandits, the thieves, the murderers. The ones who gave into evil, and now seek to hurt or steal from anyone. The unredeemed, and would probably be like the zombie. The final group were the innocents, anyone who doesn't fall into the first two groups. It was nothing but a guess, but we agreed that using the dagger on an Innocent villager would extract all of their life essence like ours. 2K LP, all at once... We also couldn't work out what would divide the passive from the rest, but it was only a theory so we put it to rest.
aw.entries.architect.sacrifice.1=Looking at these little speed runes, I have decided to see what else can I make. My goal isn't anything in particular, just improving it in some way. The speed rune was made with powdered sugar, so another type of powder should work... Redstone failed, gunpowder failed, glowstone... Glowstone worked. I currently have no idea what it does, all I know is it didn't fall to bits seconds after creating it so that is progress. The altar seems to accept it as a proper rune, so all that is left is to run a few tests in the morning. For now, I am tired and feel a bed calling me... I've run dozens of tests trying to find out what this new rune does, after two days of obsessing over this I finally noticed it. It slightly increases the amount of LP self sacrifice gives. When I said slight, I mean one rune increases LP gained by ten percent on an additive rate. For those who don't understand that, it basically adds an additional twenty LP per self sacrifice. If my theory is right, I should be able to make a similar rune for normal sacrifice... What else would the altar respond to?
aw.entries.architect.sacrifice.2=Well, if there is anything thaumaturgy and magic in general has taught me, it is that gold is a very good metal for magic. By simply replacing the glowstone in the recipe with gold ingots, a sacrifice rune is formed. It has the same additional ten percent addition per rune per sacrifice, meaning the more LP of the mob sacrificed the more LP the runes adds. This has got me thinking... The altar was constructed with both diamond and gold. It seems to resonate with the diamond enough to make the first blood orb, and even a second, so why not try making one out of gold? I'll need to revisit this idea later as a test run showed some reaction to a block of solid gold(I didn't ask Magus where he got it, and he doesn't seem forthcoming about these matters), but the altar couldn't cope with the strain needed to shape the block. Before catastrophic failure happened, a few safety precautions Magus and I had built into the altar triggered, stopping the transmutation before... Well, neither of us are really sure what would happen. But Magus said he has dealt with matters of unstable and untested magic, and knew most of them end with a crater in the floor(Hence the safety precautions). It has been a few days since the experiment with the gold block. Magus and I have been discussing what to do now, as the altar is getting bigger then the basement. Magus has suggested moving it to some of the lower basement floors, but considering just how large it might be in the future... I finally got him to agree to building it behind his home, as long as I helped put up a few wards and spells to keep people away. Currently we're planning out the structure and needed runes for upgrading. The current altar only has eight runes, but what I have planned will more then triple that number to twenty eight. The current limitation we suffer is the strength of the altar itself, as it can only take power from runes adjacent to it. My plan is to use something the altar is naturally attuned to as a way to not only increase the range of this effect, but act as a counter balance and stabilizing it to prevent it from becoming uncontrollable. For this to work, the blocks in question must be above the altar and an equal distance apart. We've run several tests and calculations, and have a layout in mind for this upgrade. Four pillars topped with glowstone blocks, twenty runes in sections of five, built one block down and two out from the last set of runes. In theory this should provide a huge boost in power to the altar without risking our safety(Well, more then we normally risk it), but until we test full scale I can't be sure. For now, we have a lot of runes to make. After much debate we've decided to invest in the "Self sacrifice" runes, as having a steady supply of mobs will be tricky. But now I face the issue of making dozens of reinforced slates... I need to think on this.
aw.entries.architect.bloodPack=It's so simple! I've discovered a way of obtaining LP for making slates that means I don't wait at the altar all day: The Blood letter's Backpack. Made with a modified leather chest-piece with a blank slate reinforced glass tank, it allows the wearer to gather LP by feeding it into the pack over time. Whenever the wearer has more than five hearts, the pack will drain their life and store it inside up to 10K LP to be dumped into an altar later. This pack does have two shortcomings, however. When the life essence is deposited into the altar, runes will have no bonus to the amount added. The second problem is that the altar will only accept LP from the pack when not performing a transmutation. Despite these limitations, it will prove quite valuable for simply allowing me to step away from the altar to do other tasks. This is a simple progress update, as it's been a few weeks since I last wrote in this journal. I've been making slow, but steady progress with assembling the slates needed for the runes and we're now halfway through. This isn't the only reason for the lack of entries, as I've been preoccupied with my research thorough thaumaturgy. An old friend of Magus has been visiting and happens to be a well versed thaumaturge, and he has been giving me pointers on how I could be doing better while Magus has been preoccupied in his library dealing with a mountain of paperwork. Now that Master Ridden has returned home, I hope to progress farther in making runes in the next few days.
aw.entries.architect.fivePeople=Or not, as it seems Fate has other far more interesting plans for us. Today we had a knock on our door while I was making the last of the runes, and after opening it was greeted by three apparent orphans. The two boys in the front looked like they were brothers, or at least close relatives. The girl hiding behind them on the other hand had a very different hair and eye color, so relation was unlikely at best. All of them were covered in dirt, clothing ripped, torn, and patched with various levels of skill. The one on the left had an old sword with a chipped blade on his side, was slightly thinner and taller then the his brother, had a few deep scars that looked recent, long and messy dark brown hair, and was looking at me with a kind poker face. The brother looked more muscled, and was staring at me with a very analytical eyes and a similar poker face, his hair was slightly shorter and lighter then his brother. From what I could see of the girl, she had a simple dress on that was almost caked in dirt and dried mud. The fabric near her elbows was becoming thread bare, and a large rip near the bottom of the dress exposed some of her right leg. Her hair was a light blond, almost pale white color, but the most shocking feature was her eyes... I haven't seen haunted eyes like that in years, and last time it was in a mirror. From the looks of them, all of them hadn't ate anything in days and seemed to have everything they own on their bodies or in the simple packs on their backs. I asked them if there was anything they needed, to which the elder brother said they were looking for master Magus. I called Magus to the door, visitors being a rare thing as it stands with ones who ask for him by name and not clearly mages being rarer still. Five minutes later we all sat in the main room, eating simple sandwiches I had prepared for us all at the request of Magus. Needless to say, this day was not going as expected. Magus spent the next few minutes practically interrogating the elder brother , asking questions on both of our minds. As it turns out the two brothers, Vlad and Demier, use to be caravan traders, eldest a alchemist with basic knowledge of potion brewing, younger with enough mana and training already to cast basic spells. The girl on the other hand had a story all her own. From what the brothers had managed to gather, her village was raided like my own was and she was evidently the sole survivor. What shocked me was when they said she was found wandering the ashs, demons having left hours before without so much as a scratch on her body. Vlad was with the traders when they found her, and he was forced to leave them if he wanted to help the girl named Bella. Their story seemed to win Magus over, as he told them he would try to do what he can for them.. Not long after that, Magus asked me to help him do some shopping for our new guests. On the way to the village, Magus asked me  what I thought of the three. "I can't say I believe all their story, but I also don't think their lying. Did you notice the girls eyes? Haven't see a look like that since...". Magus finished for me with "Your own?". I nodded at the dark memories of that time. We continued going over the details of their story, but we agreed that just leaving them in the cold wasn't an option worth considering. We decided to at least try to keep our recent endeavors into blood magic a secret from them, but when push comes to shove I think we might have to let them in on the secret. Magus let out a long sigh. "It's been years since I had to juggle this many apprentices at once. I'm going to need your help training them. But something tell me that we're going to be learning as much from them as they will from us..." I asked him why he felt that way, and he said it was just a gut feeling. I must say I had a similar feeling. Things like this don't happen without reason, and of all mages to come to they stopped at our door, something far easier said then done. An hour later we returned home, both our arms heavy with fabrics, food, and other necessities that we will need to take care of the new apprentices. I watched in amazement as the three ate, trying to make up for days with little food all at once. After that, Magus and I laid the three on various chairs and couches to rest, the strain of travel catching up with them. While Magus dug around looking for a few of his old things that we'd soon need, I started creating some new clothing from enchanted fabric for the three to replace their current worn and ratty clothing. Now that that is done, I should try to get some sleep. Something is telling me tomorrow is going to be quite busy. The Early this morning Magus and I started carving out the earth for the three new bedrooms, since the old ones Magus use to have had since been converted into a storage closet, a alchemy lab, and small study. It didn't take nearly as long as I expected with Magus using a few spells to clear away earth while I went behind using an equal trade foci to transmute it into stone brick before gravity caught on. Once that was done, Magus raised a few walls to separate the space into three rooms and a hallway. Only after that was done did I notice the three children had awoken, and were watching us as we worked. Magus told them that he had some spare furniture in some storerooms they could use, but sadly only knew of one bed. It didn't take long for them to decide Bella got the bed, the brothers would make do with a pillow and a blanket on the floor. After that we spent the rest of the day hunting for then moving furniture to their rooms, building little work areas and libraries for each of them. The following day was more of the same, with the evening filled with helping them find books to read from the grand library. Vlad grabbed as many alchemy books as possible, Demir took a few books on spell casting, and Bella took a Lexica botanica to read through. Later this evening, after the three had retired to their rooms, I sat in the main room with Magus, arms and legs aching from the heavy lifting of the last two days. I told Magus how I thought their rooms should be almost finished, and how I spent the last hour before they retired just answering seemingly endless questions. "I'm sure. I've gone through the same more then once, when I have apprentices new to magic. You were the same, I recall." I opened my mouth to protest, but closed it as I thought back to those days. I decided I probably was as bad as them, and left it at that. It's been just over a week, and the three have settled in nicely. They join Magus and I in morning exorcising, have started studying like true scholars, and have calmed the hungry nodes in their stomachs(Well, Vlad and Bella have. Demir on the other hand is another matter, but I feel he will always be like that.) Unfortunately, like true scholars and mages they've stumbled into places we didn't want them to go. Vlad has created a chemistry set that uses life essence from a blood orb as a fuel source, Demir has turned an old table into a way to use life essence to cast powerful spells, and Bella... She's left me questioning what I know about demons, after taming one and using a blood orb to bind it to her. All three of them have stumbled blindly into blood magic, and now I don't think we'll be able to stop them. Magus and I have agreed to give them a choice in whether they are blood mages or not, but I've seen the light in their eyes... I can't imagine any of them saying no. To seal their dedication to the art, we will have them make blood orbs of their own to use.
aw.entries.architect.tier3=Even with all the chaos of the last week I've managed to finish the altar construction, and it has been more successful then I predicted. With full access to twenty eight runes, each heart is now worth over seven hundred LP. I've already started prepping to try again with the gold block, having a few instant healing potions at hand. I got a brief glimpse with my sigil of sight, and this transmutation will take twenty five thousand LP to finish, over double what the altar can hold! Writing this down does give me an idea, however.... I'll need to look into it further once I have finished making this.
aw.entries.architect.magicianOrb=It was close, but I finished the orb today. I was down to the last of my three potions and barely had the strength left to hold the knife, but I pulled through and finished my shiny new blood orb! I've already used it to craft a few things I had made before, and extra lava and air sigil, and it handles it all with such ease! Now that this is finished, I have a small list of ideas to go through... To start off the process, I want a stronger slate. As it stands the reinforced type is cracking under the strain I'm starting to place on it. Well, I got a new slate type when I last upgraded the altar... Let's see if it works again. It does! A reinforced slate in a tier three altar with around five thousand LP makes a new slate... I'll call it an imbued slate for now, and come up with a better name later.
aw.entries.architect.newRune.1=First up to the drawing board: Rune of Capacity. The basic idea is simply to add more room to the altar, so a greater amount of LP can be stored at once to make bulk crafting or difficult crafts easier. Each rune adds 1.5K to the altar's main tank capacity, but what is interesting is that the "Buffer" tanks expend as well to a lesser degree. From a few trial tests(My back is starting to hurt from moving runes all day), the buffer tanks are always ten percent of the main tanks size. This will be something to keep in mind later. After helping patch a hole in the wall caused by Demir accidentally unleashing a spell, then helping Vlad dispose of a few failed alchemical "Experiments", I finally have an hour to myself to work on my ideas for runes. After the success of the last documented attempt, I've been on a bit of a failure streak. I've have runes fail to do anything, crumble to dust, blow up, and one attempt turned into cheese(I asked Magus about this, and said this isn't the first time he's seen that happen to a magical experiment. However he is just as stumped as I am over why it is always cheese, of all things). Thankfully, I finally found something that doesn't need a blast shield to use or starts growing mold after a while.
aw.entries.architect.newRune.2=Named the dislocation rune, it's only function is to increase the rate the life essence flows into and out of the buffers. Each rune increased the transfer rate by twenty percent per rune, and this effect stack manipulatively. That sounds great, until you realize the first rune only raises the transfer rate by four LP a second. Needless to say, your going to need around a dozen of these runes before you have any noticeable impact on the transfer rate. While these runes make it possible to automate the creation of slates by storing the life essence outside the altar, you're going to be sacrificing speed, capacity, and generation efficiency for this.
aw.entries.architect.magnetism=It's been a few weeks since I last made any headway with my blood magic research, needing to help with the tutoring of Vlad, Demir and Bella along with my normal duties as a mage. I've had to leave twice this month in order to sort out some mess or another, whether is be a helping with a pack of wild wolves or a bad harvest. Today, however, I managed to get some inspiration for a new sigil. When I was in the market buy more food, I noticed two children playing with a rock that was slightly magnetic. They were just running around, seeing what it would stick to and cheering when it did, or seeing how close they could push it to something before it pulled itself closer... That is what sparked my idea: A sigil that acts as a magnet, pulling items off the ground closer to you. In theory I could rig the magic on it to apply a similar effect to all items and not just metallic ones... Three hours later, and my sigil is finished. It needs a great deal of mass in the form of four iron blocks, along with the classic gold plating infused into an imbued slate(Drat, I never did work on a better name). This sigil needs to be toggled to activate, and while active will draw all nearby items to the user if they have room for it. Simple, but very effective....
aw.entries.architect.phantomBridge=I've been asked to visit a small town to settle a few complaints that have been voiced lately by them, and am currently surprised that a closer bridge isn't one of them. I've had to walk three miles to cross this river, and now must walk another two tomorrow to reach them. If only I didn't need a bridge, or had thought to grab my air sigil(I still have a bruise from the last landing I had with it, so I wasn't keen on using it again so soon.) Wait... If if I had a bridge that went with me? Better yet, what if I had a bridge that formed under my feet as I walked? Yes, that would be amazing... I'm back home after dealing with a feud over who really owned a cow(Answer: Neither of them, it wondered into town and they both thought it was one of their own), and have set out my work station to try and capitalize on my idea. I'd want the sigil to place blocks that are phantasmal, only there temporarily before fading into nothing after I leave. For that I'd need a special material... Soul sand might work... Add in a little stone for structuring around an imbued state, then use the orb... Done! Now then, for a test run... Drat, I left the auto-writing quill running again. It works better then I hoped! The sigil creates a small five by five platform under me, staying at the same elevation as I walk through the air. Not only that, but I've tweaked it so that holding shift lowers the platform by one under you, and the blocks it makes can be easily broken for a quick decent. Not only that, but it will only spawn the blocks if you are on the ground, jumping or flying will not cause the bridge to reform until you touch solid ground, unless you hold shift forcing it to form under you. And for one final convenience touch, right clicking a phantom block with a block replaces it with that block, making roofs and platforms far easier to build. That should be enough for one night, I'll dub this sigil "The Phantom Bridge" and call it a night.
aw.entries.architect.holding=Vlad asked me to help him today, requesting my assistance learning the sigils. Part way through he asked if it was ever burdensome having so many on me at once... And I told him it was. Now with a little of his help I've created the sigil of holding, and it... Well, it holds other sigils. Shift right clicking with it will allow you to place up to 5 sigils into one sigil (no physics here) and you can switch between the sigils that it holds by using the scroll on the mouse while holding shift. If you ever want the sigils back you can simply take them out whenever you wish.
aw.entries.architect.elementalAffinity=Demir hurt himself again today with a fire spell that backfired(Well, it worked perfectly if setting the ground around him on fire was the intention). In hopes of stopping further injuries, I have infused the powers of the water, lava, and air sigil into an imbued slate. This seems let the sigil act as protective aura around the user, shielding them from drowning, burning, or falling damage at a high over time LP cost. Since I asked Demir to use it he hasn't burned or bruised himself, but did pass out once from a lack of LP to fuel it. Sometimes solving one problem only makes more... But he has been learning, and I've been amazed at what his spells could do. Recently he made a long range teleportation spell and a flight spell not unlike the air sigil, but far faster.
aw.entries.architect.ritualStones=After a few days of nothing, I turned my attention to the construction of Master Magus's ritual stones. Looking them over, I might be able to replicate their usage and features using reinforced slates and obsidian... Master stone, more complicated then the average ritual stone might be possible if I use my magician's blood orb and four ritual stones to handle the complex nature of it, along with a little more obsidian... Yes, yes it should be possible to recreate them without sacrificing any usage of them. After asking him how he originally made the elemental scribing tools, I believe I can also replicate the manufacturing of them in the blood altar... But it looks like the dusk inks are too complex for this altar to handle, creating an ink closer to dark gray then midnight black... Perhaps I'll have the power to form them later, for now these four will work.
aw.entries.architect.bloodLamp=I had a flash of inspiration today (Quite literally, as watching a few town guards light a replacement torch nearly blinded me from the sudden light). They spend so much on coal and charcoal to fuel those things... What if I had a simple replacement? A near invisible light source that never goes out, can be made almost endlessly, and can easily be carried on your person? Never again would you need to place a torch to light a room, and they can ruin a rooms look(Not to mention the fire hazard they pose, ruining the look even more. "Charred mess" is never in style.). Harnessing the power of glowstone by running a "Current" of LP through it, I can create a sigil that fires a projectile that upon landing forms a stable light source par with that of torch. The only sign of its existence is the light it gives off and faint red particles. While it uses a small amount of LP, the advantage over carrying stacks of torches is enough to justify it.
aw.entries.architect.boundArmour.1=Today Magus gave us a lecture on armors, going over the practical applications(Protection from large pointy sticks shoved in your direction and holding magic to improve the abilities of the wearer), typical construction of armor, and notable armors through the ages (And the very fatal flaws that their owners eventually discovered, much to their short lived dismay). Seeing and reading all this, I feel it is time I take my own try at this art using blood magic. Testing a wide array of materials and structures, I discovered a process to make an almost indestructible metal using a casing filled with life essence with glass hardened with diamonds and reinforced with bright red blood shards(More on their properties later) Magus showed me a while back (Making the prototypes of this armor took longer then you would believe). The process needs the soul of a demon bound to it, but this seems to be a small price to pay. 
aw.entries.architect.boundArmour.2=To start, you need to make a "Soul armor forge". This block summons the demon soul and controls the transformation of the "Socket" blocks into proper armor. By placing the filled socket blocks around the soul armor forge in roughly the shape of the armor piece you want with the forge in a gap, you set what it makes. Right clicking the forge will then finish the process, creating the armor piece and binding the demon to the shards inside the armor. You will need to bind the armor to yourself, allowing the armor to drain your network to repair any damage it takes(Another benefit of using blood shards in the creation). After donning the armor(Making a few adjustments, still refining the creation process), I asked Magus to help test just how resistant this armor really is. Several nasty bruises later, we have decided this armor shields the wearer from ninety percent of damage from mundane damage sources. What is interesting is that it also blocks roughly eighty percent of magical damage, meaning many armor piercing methods are far weaker. The metal also doesn't seem to hold enchantments, and the demons inside reacts quite negatively to our attempts to do so. Using the ritual of unbinding, we are able to deconstruct the armor back into the component sockets and anything that was inside them. I mention this because we seem to be able to "Augment" the armor before creation by adding a combination of sigils, blood shard, and an orb. The sigil added to the armor piece(You can add anything to the socket by right clicking with the item in hand) determines the effect, and adds a passive cost to wearing the armor piece. The blood shard lets you add an upgrade, the weak shards we have at our disposal only allow one upgrade but in theory a stronger shard should allow more to be added. Finally, the orb seems to lower the cost of the upgrade. Every sigil seems to have something to do with the normal effect of the sigil, either giving you a passive bonus or having the effect of the sigil happen automatically around you. A few examples are a water sigil making you immune to drowning, the fast miner always active, and a green grove sigil acts around you.
aw.entries.architect.sanguineArmour=I've managed to use my new found knowledge of armors and experience making bound armor to try and improve thaumium armor using blood magic. By throwing goggles of reviling or thumium chestplate, leggings, or boots the ritual transform the armor into a "Sanguine" version, with a small vis discount and protection par that of pure diamond. What is really interesting is that this armor can be augmented with runic shielding, and then socketed into bound armor in the same fashion as a sigil to pass on the runic shielding, with the bonus that adding the sanguine helm to the bound helm well allow the wearer to see things like aura nodes as if you had goggles on. I should note that you can only socket a sanguine armor piece into a same armor type(Helm in the helm, boots in the boots...), and that any vis discount is lost when you do this.
aw.entries.architect.soulSuppress=Having the armors special effects all the time has become a little bothersome. To deal with this I have managed to create an "Armor Inhibitor" to repress the special effects it offers in the cases they are more bothersome then helpful. Unfortunately, it is a bit all or nothing in this regard. 
aw.entries.architect.ritualDiviner=It's coming up to the three year anniversary of Magus taking my on as an apprentice. Time seems to have flown by while I've been engrossed in research on applications of blood magic and working with Magus... I should make a gift for Magus to as a thank you for these last few years. Considering the complex nature of his rituals, an aid to building them might just be the perfect gift... Ugh... Making a way to build rituals easier proved to be quite the task(And it reminds me of the rule of conservation of effort...), but I finally have it ready in time for the anniversary. While it isn't cheap to craft(Costing the four elemental inks, four diamonds and an emerald), I've gone to pains to make it easy to use. Shift right clicking will cycle through a preset list of rituals, and shift right clicking on a block with also rotate the direction the ritual will face(For anything like the ritual of speed, where that matters). Shift left clicking will go through this list in reverse, for those times you accidentally go past the one you want. By normal right clicking on a master ritual stone the diviner will take stones out of your inventory, place then in the world and ink them to build the set ritual. To make it clear it is working, it emits green particle effects whenever it places a block successfully. However, it can only place a block in an empty space. Anything at all in the way will stop the process until the obstruction has been removed. It is a small annoyance, but nothing major enough to risk breaking the diviner(Again). Magus adores his gift, having rituals faster and easier to build is the one thing he could actually use(That he doesn't already have). The fact it makes anyone who uses rituals lives easier is a just a bonus at this point. As it turns out, he got me a gift as well: A pair of ichorium wand caps, the one thing I had been dreaming about for a long time...
aw.entries.architect.bloodShard=Reading through my older entries for inspiration on things to do with our current power(I believe we have started reaching the limit, struggling to stretch what we have as far as it will go) I noticed that I "Might" have forgotten about covering the blood shards like I said I would a few months ago... I'd best start from the top. Blood shards, as Magus has likely stated already, are fragments left behind after vanquishing a hostile mob with a demonic weapon(The Bound sword or Energy Blaster). Upon close inspection, I see small strands that looks shockingly like that of our soul networks... Well, in theory anything with a "Soul" could have a network, but this is a surprise nonetheless. Because of this, the small shards contain a vast pool of energy that our magics should be able to tap into. A rather unfortunate first experiment involving stone showed that this energy can be wielded to create or transform matter(I believe several others have come across similar principles to convert energy into matter using science). I say unfortunate because I was stuck inside the lab's supply closet for a few hours to hide from the rapidly expanding stone as the released energy of the shard grew the stone to thirty two times it's former size, filling the rooms and pushing out the open door and into the hallway. (Further crafting of blood stone was done outside after that...) But regardless, the blood shards potential energy and connection to the soul network is how we manged to form bound armor so easily. Thinking about it... Blood shards might be the answer to our power problems. The last time we needed an upgrade, we used something related to the altar... Perhaps using something related to the soul network is the next step? 
aw.entries.architect.tier4Altar.1=Once more I must make a progress update, as daily life as taken up most of my time these last few weeks. Magus has started offloading some of his work onto us, dealing with small problems and formal matters, bandits and demons, and general mage work. I have been able to do a little work on the altar or blood magic in general over the last few months because of this. Thankfully, we've burned though most of the grunt work that has piled up and I now have some time to conduct our research. Magus and I have spent the last week doing the calculations for upgrading the altar, determining that bloodstone should work for the capstone, and now it is just a matter of seeing how many runes we can get away with adding without nasty side effects or it all just blowing up(The other three have placed bets on this, I believe). 
aw.entries.architect.tier4Altar.2=After another few weeks of testing, balancing, and error we have finished the altar upgrade. This new tier adds twenty eight more runes to the altar's structure(Seven on each side, for a grand total of fifty six), and four pillars capped with large bloodstone brick(This was the best material we could find that doesn't expire or fall apart. Or, in one memorable case, actively try to kill us). With these additional runes we are projected to be making roughly one thousand three hundred LP for every heart sacrificed, a hefty improvement from our seven hundred from before. Unfortunately more work has come in for us, so once we have the altar built it will be some time before we can focus on the hunt for the material for the orb. Current plan is to start with precious materials like before, and work from there. Ah, innovation... Ten percent work, fifteen percent knowledge, five percent patience, seventy percent trying random things to see what happens. More work then expected flooded in at once, and we have been struggling to keep up with it all. Vlad has been acting as a healer and doctor trying to deal with a plague that has going around (Caused by a witch-in-training using the wrong vial in a love potion), and has managed to keep it from running wild. Demir has been clearing out known bandit camps, and has enjoyed a chance to test out the spells we've helped him build(Only in the mage guild are bandits considered an endangered species. This is in part due to the average intelligence level of said bandits, and thinking that an area where there are people who, sometimes quite literally, shoot fire out of their hands, mouths, and other places(Mostly the apprentice mages, as a joke or party trick that has often gone very wrong) is a great place to set up shop.). Bella meanwhile has been dealing with the demons that have been wondering in, the mage guild  acting as a sort of beacon to them due to the thinness of the fabric of space here caused by countless magical experiments, both successful and not. Magus and I have been going through a huge pile of letters(Seriously, where are they all coming from, and how do I not notice the pile building up until it flows off the table?) from local villages and mages, ranging from simple questions about an issue or problem to requests for assistance in matters ranging from theft to flooding. We've been coordinating these issues, mapping out journeys to deal with as many as possible on one trip.  As a reference point for how long it has taken us to deal with it all, dust has gathered on this book since the last time I wrote. Thankfully we've sorted out the letters and referenced the villagers to mages who are closer to them(Much to said mages chagrin), so it should be a while until this is an issue again... Thankfully all this time has left me with a few ideas for future projects.
aw.entries.architect.masterOrb=The others have tried countless gems and metals trying to create a new blood orb, all of them either did nothing or created a flawed orb(Cracked or chipped, extremely fragile or incomplete). But last night I had an idea from a dream... The weak blood shards were once part of a soul network, so what would happen if we tried to reforge it into a whole orb? It is better then any other idea we've had, so I might as well try... Success! While we did burn through several instant health flasks, we now have half a dozen new blood orbs. While we haven't named them yet, they are a true master piece... A Master blood orb, that name has a nice ring to it... I'm getting side tracked again, but I must remember to suggest this name to the others(It at least is better then the temporary name of "Shiny yellow blood orb"). With this new found power, I might be able to bring to life some of the ideas that have been just out of reach up to this point. The first interesting property of these new orbs has been shown in our bound armor. While lesser orbs simply lower the cost, this orb is able to negate it entirely! A simple upgrade, but a very useful one...  Another altar tier, another slate tier. Dubbed "Demonic" after the insignia that has formed on it's surface, we should be able to put this to some good use... I've also noticed that a demonic slate can be used with a weak blood shard and master orb to "Grow" the shard making four more. Going over my earlier successful experiments, I might be able to tweak a few concepts used to achieve a new result. I'm starting with the fast miner, and if boosting my arms was this useful then what would happen if I augment my legs? Using cookies, sugar, and some of the normal materials I have created a new sigil. Time for a test run... Two hours(And two very sore legs) later, I've concluded the tests. The "Sigil of Haste" boosts the users leg muscles, allowing you to both run faster and jump higher then normal. The best part? It seems to stack with the effect of jump boost and speed(Which Vlad was kind enough to brew a potion for testing). Now then, I need a rest after all this...
aw.entries.architect.whirlwind=Continuing with my thought process of "Reworking the old", I have used a master orb, demonic slate, two ghast tears, and feathers to reform an air sigil. The "Whirlwind sigil" changes the core property of the air sigil to have the winds not effect the user by propelling them, but instead creates a bubble of wind around them pushing away many forms of projectiles. I should note that not all forms of projectiles can be pushed back by the winds, and it has a tendency to effect your own shots. It isn't perfect, but it is useful enough to have around in a pinch.
aw.entries.architect.compression=After hearing the local miners complain about always having to stop work because they've run out of space in their packs, I've built "The Sigil of "Compression". While active, the sigil will check your inventory for anything that can be compressed into a "Storage" block(A two by two or three by three recipe of that item that can then be crafted back into the item. An example is redstone into redstone block.) and squeezes it together for you. Simple, but extremely effective...  I should note I added a few special cases to the sigil, such as glowstone dust to glowstone and a restriction to only compress cobblestone if you have more then a stack.
aw.entries.architect.severance=Bloody endermen... Magus sent me out to try and stock up on ender pearls, but the pests keep warping away from me only to pop up later(And almost always when I don't want them too). It is just after dawn, and I've only managed to bring back six pearls... But maybe that is enough. I recall seeing some research notes on endermen and how they are able to teleport somewhere in the library. It is only an idea, but it might be perfect for this task... Eureka! After reading through the notes, I worked out how endermen connect to The End as a power source for their teleportation. Using a few ender pearls and eyes of ender, I've completed a sigil that acts as a sort of "Signal blocker". In layman's terms, it disrupts the connection to The End and renders them incapable of any teleportation. Not only that, but it also prevents many forms of magic that relay in similar methods, such as Demir's spells and the barbaric(But nevertheless effective) act of throwing an ender pearl. From the light, I'd say it is almost sunset. Time for some well deserved payback...
aw.entries.architect.teleposer=After my research into the basics of teleportation, a thought struck me: As it stands we don't have an established transportation network for mages to use, instead using the long and winding roads or other means of land travel. Sure, a witch might have a few way stones or a broom and a spell caster has a convenient recall spell, but we don't have a dedicated transportation system. Considering how much Magus and I travel to deal with small matters, and how often mages visit each other it is a surprise no-one has built something like this sooner! After talking with Magus about this, it turns out something like this has been tried before. The main failing point each time was the limited number of people able to use the method established. (A thaumaturge rarely knows how a waystone works and so on.) This has prompted me to create a means of teleportation that, once properly set up, almost anyone can use if they know how to push a button. Further research into ender pearls is required... After close analysis, I've decided that my first priority should be the improvement of the base ender pearl. As it stands, they are a little too fragile and random for reliable long term transportation. By placing a pearl inside a T4 altar I have been able to refine it into a focus, stronger and more predictable then the base pearl. I am currently in the middle of creating a mechanism to hold and operate the more complicated bits of the process. My materials for this is mainly gold(A metal that hasn't shown any side effects to the ender energies used), two ender pearls(One input, one output), and the focus itself to handle the conversion.  I've managed to finish building two of the "Transportation of matter across two relative positions devices", or teleposers for short. How they work is simple(-ish): To perform a teleport you need two teleporsers. Next, bind a teleposistion focus to one of them by right clicking on the teleposer, bind it to yourself by right clicking on anything else, then slot it into the second teleposer and apply a redstone signal. If the focus is bound to teleposer A and placed in B, then teleposer B is triggered with redstone, everything above A will move to B, and everything above B will move to A(I hope this is clear enough description, it is hard to put into word the process. If you try it yourself, it should be clearer.). This means that if you want a two way transport, you can have two foci(One in each teleposer) and trigger the one you stand on, one have one focus and always trigger the same teleposer. In theory, we could build a "Transport Nexus" by having an array of teleposers bound to the same one(One A, multiple B's, triggering the B's to do the transport). What is great about these teleposers is that they don't just transport people, but any mobs or blocks in range as well. It even functions on complex blocks like furnaces and chests! With a little redstone logic, amazing things should be possible with this simple(-ish) system! With the completion of the final prototype, I should get some sleep... A new day, a new idea! The basic teleposition focus only transports a one meter cubed area above it, but if I improve the focus a bit in the altar it will be able to handle a three meter area! Not only that, but it can be further boosted by adding a weak blood shard so it works in a five meter area, then these new demonic shards that Bella has been gathering for a final upgrade to seven by seven by seven area. I should mention that the cost of each transport is based on how much is moved, and how far it is moved. It also has the quirk of being unable to transport people or mobs across dimentions.
aw.entries.architect.suppression=After a recent flood where Magus built a ritual right in the middle of the village to save a huge chunk of it, I've decided to try and replicate the effect on a smaller, and much more portable, scale. Thus, I've made the first version of the "Sigil of Suppression". What it does, in layman terms(You don't wont me to get into the back end details involving temporary storage in a pocket dimension) is create a bubble around you that removes any liquids, replacing it all as you leave. Using this sigil, I was able to take a leisurely stroll under the nearby lake. I am glad I remembered to bring my elemental affinity sigil however, as the early prototype fail on the way back, so I had a very soggy trip home... I "Think" I found the cause of the issue, and have patched it.
aw.entries.architect.superiorCapacity=Taking a break from the sigils I've been making over the last few months(How time flies...), I have shifted my attention to improving the runes we have available for our altar. After going over the design of the capacity rune, I have found room for "improvement". I have that in quotation marks because it only becomes greater then normal capacity runes if you have more then fourteen of them. You see, the "Rune of superior capacity" adds ten percent to the altars capacity, and this effect stacks multiplicatively(Layman terms: The more of the rune you have, the stronger the next rune will be) instead of the normal capacity runes static rate. Powerful, if a niche rune.
aw.entries.architect.orbRune=Considering the amount of LP we burn through on a daily basis running our sigils, powering our spells, fueling the rituals, and feeding our tools and armor, it is a miracle we don't run out of LP more often then we do. Even with our soul network topped off with a master orb, we're tapped dry within a week. Just upgrading our orbs has served us so far, but that has shown to be too unreliable and a huge investment with the altar... Wait, we're always using our orbs to make runes to improve the altar, so why can't we make runes that let the altar improve the orb? It can't be a one way road, can it? Yes, if we tweak this here and shift pressure here... I'm rambling again, I must talk with the others about this! Yes, it works! By focusing the power of three blood orbs and using two demonic slates, we've created "The Rune of the Orb". This boost the altars ability to feed an orb(Think of it like pressurizing air, more of it in the same amount of space), letting it add an additional two percent per rune(Additive rate, so more runes won't change how much it adds). With our master orbs, one rune is an additional twenty thousand LP. While that doesn't sound like much(To think, I use to be limited to only five thousand...), consider the number of orb runes one altar can have...
aw.entries.architect.fieldTrip=After hearing the demands of the younger apprentices, we are leaving tomorrow for a quick tour of the magelands, and expect to be back by the end of this month. Talking with Magus, I agree that we've been pushing ourselves hard lately with work and research. A little rest during a vacation might be just the thing I need to gather ideas for future projects... It will also give me a chance to talk with other mages about setting up a teleposer network. It is a vacation, so a little work on side projects you normally don't have time for is normal right? Talking with Magus during the long hours on the road, a few details about the mage guild were covered on this horseback lecture. Largest of the "Nations", most of the tech focused nations won't touch this area due to the high levels of magic in the land, air, and water causing technology to become a bit more unpredictable(Read: Unstable and often highly explosive), and have instead left it in control of the mages. As a result of this, thousands of kilometers of land filled with river valleys, harsh deserts, frost and snow covered forests, fertile plains and countless other biomes have been untouched for us to expand into. To this day there are people who venture out into these untamed lands hunting fame and fortune clawing over mountains and through the ancient ruins that litter the area. However, the vast majority of settlements are close to the borders with the other nations, thriving off trade(Their gadgets might not function here, but the results are another mater. Most of the mining towns send out the harvested ores across the borders for processing).  The last tally was done roughly five years ago, with the number of total settlements at around three hundred scattered thin across the mage guild. Of this, we have roughly seventy active mages and thirty classified as "Apprentices" to manage village needs and deal with problems as they arise. Generally one mage would managed half a dozen of the closest villages and any apprentices they take on comes from this area however, it was not uncommon for mages to offload work onto other local mages if they have not the time or expertise in the field and for them to take on students who seek them out as tutor.   That's enough for tonight, my watch is almost over and I feel sleep calling me. I'll write down any more interesting details I learn tomorrow. Magus said we should reach another village tomorrow, and he knows an inn there that should take us in for the night(Just in time as well, I don't trust those clouds on the horizon). Today's trip was more of the same, ridding through a dense oak forest while Magus went over anything and everything about the area around us and the Mage guild in general. Mages seems to have a list of unwritten rules governing how to act, what to do, what to say... For example, it is recommend to live at least a mile and a half from the closest village. Far enough away that people won't bother you with trivial things and you keep a mysterious reputation with the people, but close enough to be convenient for buying what you need. Another example pertains to marriage: A mage is free to wed whoever they like(Even other mages), but the spouse is entitled to most decisions and any compromises favor the spouse. Being wed to a mage often causes problems for the spouse, often with relatives and friends, and is typically a huge adjustment. Doing anything to try and make it up to them is common, as the mage feels themselves a burden and cause for their loved one's strife. From what I've gathered and how Magus speaks about the topic, these are more general outcomes and human reaction to the same events that Magus has seen happen time and again instead of rules or traditions passed down from master to student. One final interesting fact is that most mages send their children to learn the art(If the child so chooses to take on magic, and the have the talent for it) with another mage, preferably one they have rarely or never met as to prevent any bias and favor. I was right about those clouds, and we have reached the small town of Salis soaked to the bone. We're all currently sitting by a fire in the Mundes inn letting our robes dry and bodies warm from the chilling rain(From the clinking outside, now hail). I plan on acquiring another books tomorrow, and writing any more facts I learn from this trip with Magus in it instead of spending the pages of this book(Already longer then I ever dreamed it would be). According to Magus, we should be able to meet another mage in two days time, a woman by the name of Athis who is skilled in botany and herbs(Any medicine made by her always trades for a fair few coins). On another note, what Magus said about about people being wary of mages is holding true. The common room has been divided into two parts since we got here: The side where we are, and everyone else who have gone a table or two away from us and keep glancing at us as if to make sure we don't do anything without giving them a chance to run. The owners of the inn, a small family known as the Eldritches, seem to be the only ones who don't seem to mind us. Their daughter, or at least someone the right age and look to be their daughter, happily walks up to us asking if we needed anything other then rooms while we rested. Vlad and Demir order food for us all, Magus inquired to the drinks available, while I have asked if she knew if the general store had any writing journals for sale. And now I'm at the point of writing things as they happen... I needs some sleep in a nice bed after all this riding(But it looks like Demir is more saddle sore then I am), and the inn keepers have giving us cheap logging in the attic with a few decent, if old mattresses in a warm room with a roof over our heads. We left the next morning after a short trip to the market(Where I managed to get a good deal on a fairly nice journal, and have already filled out a few pages on my watch last night), and now are almost at Athis's residence. We currently are taking a short rest to eat and have the horses cool off after a long trip before riding the last leg of the journey after dusk has set. I'll detail anything interesting that happens. Talking with Athis, who I have already detailed in the other book along with most of our conversation, I managed to steer the conversation to how often she needs to travel for herbs, and if she has ever been in a situation where she can't get access to anything she needs but another mage might have an abundance of. As a response, she handed me a list of everything she buys when ever possible because of how rare it is here and the versatility or uses of it. The list was ten centimeters thick. Seeing a chance to get another mage's opinion, I mentioned a recent discovery in the field of magical transportation see might be interested in seeing. At this point, she gave me a wary eye and said she still has a bag of purple chalk and a stack of waystones from the last person who said that. Inviting her outside for a demonstration using a few of the teleposers I had packed, I showed step by step the process and how simple it was to use.(It took her a moment to recover from the initial shock of teleporting, but now she takes it in her stride.) Going over a few more details inside with hot tea, I asked if she would be interested in us setting up a link in the area nearby for her to use as a means of transport. Frankly, I was nerves enough at this point that I thought see would just laugh at my new goal. Instead, she was almost begging me to set a few up around a few locations several hundred kilometers apart. At this point I felt comfortable to tell her about my grand plan for setting up one of these near every mage in the guild and at some important points, so the days of needing to travel day and night to reach another mage would be over. Not only was she on board with the idea, she even offered to help us set them up if it meant that it would be ready faster! We spent the next few hours talking about this late into the night, covering little details and about enlisting the help of other mages to set it up.
aw.entries.architect.bindingKey=Well, this has been an interesting trip... We've been visiting every mage we pass, talking and showing off the boons of our blood magic. Most of them have been extremely accepting of our arts, and the ones that haven't been at least aren't opposed enough to get violent about the subject(However, all of them are still welcome to the idea of a transport network). We have noticed one problem on our trip, however: We can't easily give people the things we've been working on, as they need LP to run it and it takes time to reach a point of being able to support the usage of them. We've gotten around this somewhat by binding the items we gave them to ourselves, but anything they make once we've left will be near worthless to them... THAT'S IT! I could make a device that mimics the binding of an item, copying a thread of our soul network to another device, so anyone can craft something new and have us power it. I can use a weak blood shard to hold the soul network thread that it uses to pass on the link... Yes, I should be able to make this easily. I really should have slept after getting home, instead of going straight to building something... It took me three hours to think of using gold for the "Key of Binding". Regardless, it has been finished. All you need to do is right click with it to set the original owner, then hand it off to the person you wish to use it. Then they right click with it, and it will bind the first thing it finds in their inventory without an owner to the person the key belongs to. While it's uses are limited, it is something you want to have around in case you ever need it. 
aw.entries.architect.tier5Altar=In other news, we've started talking about what we do now that we've started reaching the limit of our power once again. I've calculated the number of runes we would need, and have in fact already crafted all fifty two of them. We even have a few guesses on what to use for a blood orb, starting with these demonic blood shards... The problem now is the stabilizers we need. There is only one thing I've found that can handle the strain of the altar: The raw power of beacons. We need four bloody beacons. One is near impossible, four is... We need a miracle to get any farther. Magus says he has some plan, but I think even he is going to struggle with this. Talking with the others, I believe I should focus on setting up "The Nexus" instead of struggling with the limits of our master orbs. This is going to be a project that will take months... I've decided to limit the usage of the Nexus to mages, as public usage would tax my network night and day. I start tomorrow on this task, and plan on cleaning out some ruins about a kilometer to the east of us to set up the Nexus base. I already have a few dozen teleposition foci crafted, and enough teleposers to set up over a dozen locations. First on my list, Athis... I'll leave this book here, and will document anything that happens in my other book. Once I start research in blood magic, I will write in here again.
aw.entries.architect.priceOfPower=Magus... Magus has gotten us a reservation to kill Withers. Not only that, but we each must kill one on our own. I said I wouldn't write in here again until research started again, but I felt this is significant enough to mention... We are so close to being able to upgrade the altar. If we live through this trial, that is.  I have written the details of the last few days in my other book(It has gotten far more use then I ever expected), but we have them. Enough nether stars to finish our altar, enough to experiment with, enough to make being stuck in bed with this broken leg worth it all. I have been hard at work since my leg healed, ideas gushing out of me to the point I have almost stopped eating and sleeping(Thankfully, one of the others tend to drag me out occasionally). Time to document the fruits of my labor.
aw.entries.architect.demonicOrb=Our assumption about demon blood shards was right, and after a long crafting process we have the Archmage orb. We had to spend several hours trying to fill this orb, and finally hit the cap at ten million LP, ten times the size of the master orb... We also have graduated up to the slate dubbed "Ethereal". I know this isn't much fan fare, but this has become a bit of an expected event.
aw.entries.architect.energyBazooka=First thing I created with this new orb was an improvement to the energy blaster, as I felt it just wasn't strong enough during the fight with the wither. Harnessing the power of a few reagents and a demonic blood shard, I have created the "Energy Bazooka"(Not a clue what a bazooka is, but Demir tells me they shoot big explosions). Costing twenty thousand LP a shot, it launches one main projectile that upon hitting something explodes into twenty more smaller explosive projectiles to devastate other living things around it. I have tried tweaking it so that terrain isn't harmed, but there may be minor damage depending on what is hit. There is a "Slight" problem of recoil, but nothing we can't live with.
aw.entries.architect.accelerationRune=Moving my attention on to runes for our wonderful new altar, I had a revelation as I noticed a speed rune next to a dislocation rune: Increasing how much LP is moved from the buffer is fine, but how about increasing how often it transfers LP? Using a few materials like buckets and an ethereal slate, I have transformed a normal speed rune into something far greater. Instead of affecting crafting speed, one rune will lower the delay the altar has on moving the contents of the buffer by a twentieth of a second per rune. I have noticed a strange limitation, however: Any runes beyond nineteen seem to have so little effect it isn't noticeable anymore, so you can only have the altar transfer twenty times a second. I will need to run some experimentation on the best ratios of acceleration to dislocation runes, but I suspect that the capacity will also be a factor.
aw.entries.architect.harvest=Bella has dragged me with her to town so she can be sure I'm out of the lab for a while(I have noticed I'm a little pale, but I just assumed that was from all the sacrifices I've been making lately). As we ate two sandwiches at a bench on the edge of the village, I watched the farmers in the field slaving over the harvest while trying to outrun the storm just visible in the distance. Talking with Bella about ways to help them, I created the basic idea for the sigil now known as the "Harvest Goddess". It is to the "Reap of the Harvest Moon" what the "Sigil of the Green grove" is to the ritual of the green grove, performing a similar effect to the harvest moon around the user by summoning a little demonic imp(You can thank Bella for this part). As soon as we had a version ready, Bella ran out into the field with a green grove, haste, and magnetism sigil to quickly run through the field collecting the harvest behind her. Needless to say, the few farmers in the field were awe struck by this sight and a few of the newer ones thought she was a goddess sent to answer their prayers(And I liked the sound of it, so we now have a name for the sigil).
aw.entries.architect.demonProblem=A mage can never get a good rest without something happening... Magus has finally launched an operation to deal with the demon portal, and has asked Vlad, Demir, and myself to lead the siege team. He has cooked up some complicated plan that only he knows all the details, and in fact might be the only one who even has any idea if it might work (Quite a few of us felt lost when he gave the basic overview of the plan). One question asked at the meeting that felt quite memorable was "Wait, how are you able to replicate this portal?" Magus only smiled and asked him "At what point does science stop being science and starts being magic? It is the same principles and theories at work, just a different means of execution." For Magus, that seemed to answer everything. Gah, I need to focus on packing for the trip as we won't be back for a few weeks. I feel like I'm heading out for war, and I guess in a way we are... Alright, I did not expect to find a camp of demons when we warped to Venric's folly. I expected a hole in the fabric of space, I expected a few demons, but a little village... Not to mention the fact that I had to save a fairly inexperienced thaumaturge who hasn't dealt with demons before. He rushed in, thinking he would show off a bit. To his credit, he did manage to kill a few demons before the hoard took him down. I had to run in with a teleposer to save him, but we did discover something important: From the vanishing remains of the demons, I retreaved two crystals. One was a shiny red we have named a "Life shard". The other a brilliant blue we have dubbed a "Soul shard". What is odd is that my bound armor seemed to react when I touched them, and I saw a few stray strands of soul threads between the demons and these shards. But that isn't what was really odd. I was spotted by one of the demons when I went to save the fool, and noticed two things very quickly: The demon had a connection to the portal with a few soul stands.  The second thing was that it seemed to be... Vibrating? Closest word that comes to mind when I think of it. Shortly after that, two things happened: The demons started running towards us from every part of the village, and for a few hours after we left the camp seemed to be under a lockdown. The demons had donned armor, and were patrolling the area instead of building. These guards seemed a little stronger then the others, but tomorrow I plan on mounting an operation to collect more of these shards for research...
aw.entries.architect.tier6Altar=From what we have learned so far, these crystals seem to be ordinary crystals (Well, ordinary as anything from Tartarous can be here) filled with demonic aura and... changed by the travel through the portal. These demons have been using them as a sort of personal anchor, an extension of the main portal's influence. I have discovered that mixing these crystals together in a five to four ratio of life to soul shards forms a purple block that is practically pulsating with demonic power. Since we may be here a while, I have started construction of a prototype blood altar that utilizes these new blocks for enough stability for a T6 to be possible. I have yet to try this, but if it works... Once again, a theory proved to work out. With the addition of four pillars capped in these crystal clusters, we are able to support another seventy six runes, nineteen on each side of the altar. I have also tried the clusters out for making a blood orb, and the "Transcendent orb" was the result with a cap of thirty million LP.  Sadly, I haven't had a chance to experiment with this new altar or orb, so I haven't anything else to share yet. 
aw.entries.architect.moreThanHuman=Magus and I have been looking into the oddity I noticed with the demon crystal and bound armour, and what we have reaped from it was far beyond what I had expected. I has assumed at first this would simply allow more sigils or other things slotted into our armour, but that was far from the truth. As it turns out, the demon soul in the armour was reacting to the crystals and was becoming more... Active? Present? Detached? All of these are accurate, but none of them are quite right. Regardless, strands of the demon's soul become... Loose? In my head this is makes sense, but on paper it just sounds odd.  Let's start that again, no matter how odd it sounds. The strands of the demon soul became loose from the armour in some places, and I noticed them interacting with my own soul strands leading to the armour, twisting and tangling the strands. And as they touched my soul strands, I felt... Something. I can't put it into words, but I knew it is something to look into. Mentioning it to Magus, he made reference to tales of old arch mages making pacts with demons for a bit of the demon's power. With this train of thought in mind, we started the road that lead to what has recently born fruit: Project Omega. The concept was simple: Fuse the soul of the user and the demon in the armour, then see what results from it. The stories of what demonic power can do vary widely, so we honestly had no idea what to expect. What we got was the Omega state. Long story of testing short, we found that reagents made the perfect medium to hold our souls and that of a demon together, with as few repercussions as possible. What was interesting is that the reagent used seemed to... Colour(?) the demon soul, attuning it like those of the Demon invasion. Depending on the reagent used, the powers and abilities offered changed drastically. We gained power beyond anything expected, with the armour reshaping itself to suit it's new abilities. While there are too many to list by name, I'll cover a few general features. First, we haven't reached a point where the Omega state lasts forever. We use reagent to power the fusion, and to maintain this fusion it slowly burns through the reagent. While Magus has made a ritual to offset this, the range and the cost mean it is far from an ideal option. In order to ensure we aren't caught of guard by reagent running out mid battle without warning, I've managed to create a visual display in the helm(Located on the left side of the screen, next to the LP bar, or at least would be if you have a divination sigil slotted into your bound armour). We also found that the demonic power gave our armour it's own buffer of extra health, taking damage that normally would have pierced through armour and hurt us. I added another bar to represent this, underneath the other two bars. I should note that this "Reagent health" needs reagent to form, so the more damage it takes in for you the less Omega time you have. Depending on the reagent used for the process, we discovered that the area around us had an impact on us. We gained boons from each armour, with extra health and stronger strikes being common place among all of them. However, this weapon can cut both ways: While we gain boons in favourable areas, in hostile areas we would suffer debuffs and weaknesses. Water armour is great in an Ocean biome, not so hot in the Nether however. This is something to keep in mind before starting Omega: Don't pick one based on effects alone, but where you expect to be fighting. I should end this entry here for now, as we are still working on Project Omega. As of this time of writing, we have only managed to enter Omega using Terrea, Aquasallus, Incendium, and Aether. Any other reagent we've tried will need some fine tuning to have working, as we needed to customise the ritual for each of the four we have. We have also noted some odd behaviour from the armour when in use, trying to expend energy in odd ways. I plan on looking into this at a later date.

aw.entries.rituals.intro=At the constant demands of my apprentices, I've started writing down my knowledge of blood magic.  I've told them time and again that what we do is far too dangerous to write about, and in the wrong hands... I don't want to think of the consequences. But they have made one good point: if more people are going to learn blood magic, word of mouth is far too limited. But I'm getting sidetracked, back to what this is all about. Following their example, I will introduce myself. My name is Magus Arcana, and I am the founder of blood magic. I have lived a long life, studying more fields of magic then one could count. When I was younger I moved to the outskirts of a village to start my journeys into the arcane. After several decades of study, I realized there was one source of power few had ever touched: Blood. Many mages claimed that the use of blood in magic was taboo, yet gave no reason as to why. Eventually, my curiosity grew beyond my reluctance and I started experimenting with the art of blood magic. 
aw.entries.rituals.weakRitual=My first breakthrough was with a simple device that used the power of whatever was above it as a template, along with a great deal of blood(25 hearts, or 5k LP if you go by the system one of my apprentices made years later) to perform small miracles. I must also note that at the start of each of the rituals lightning strikes the ritual stone setting it alight, as if the cost of activation wasn't deadly enough. After two weeks of meditation to strengthen my soul to the point where I could handle the strain of these rituals, I started experimenting with different templates to discover their effects. At that point, I only had the strength to test one template per day before I felt weak. I decided to start training my body and mind again like I did when I was younger, while using a few tricks I had learned over the years to help speed up the process.With some work I found that water creates a rainstorm with so much lightning that I couldn't sleep that night. A somewhat rare mineral block by the name of Lapis had the very interesting effect of turning day into night. Needless to say, everyone in the village was very confused when the sun suddenly vanished and they couldn't see anything. I was shocked when I found that a coal block summoned a zombie stronger than any other I've ever fought in the night. I also lost the coal block to fire from the lightning strike when I was dealing with said zombie. The final effect I found was with bedrock I found at the bottom of a local mine, hardening my skin temporarily. While I looked no different, I could shrug off a blow from an iron sword without a scratch. A bruise perhaps, but not a scratch.
aw.entries.rituals.rituals=I soon decided that those rituals were far too weak and costly, so I started working on a much larger version. By mixing my blood and a little bit of Mana with an item that was naturally attuned to an element(I found that magma cream worked for fire, a Lapis block for water, obsidian for earth, and a ghast tear for air) I created powerful scribing tools that used blood (100 LP's worth per use) as ink. I used stone fused with blood and obsidian to create stones strong enough to withstand more powerful rituals. It took me over two years to perfect the ritual stone. Trust me, you don't want to see what happened with some of the earlier tests. Let us just say they made the holes creepers leave look like potholes, and leave it at that. I also designed a "Master" stone to be what really controls the rituals, with the powerful dust called "Redstone" able to deactivate rituals and putting in a few safeguards so that if some fool tries to activate a ritual they don't have the strength or the blood to handle, it doesn't kill them outright. While I was able to weaken the pull that rituals carried on the user's life force to not cause any lasting harm, I could not quite get rid of the feeling of nausea and unease an unattended Ritual would cause. I eventually got tired of trying to stop it and marked it down as an occupational hazard. Once I had the stones, I realized that there needed to be a bridge between myself and the "Master" stone of the rituals. I found an old, red crystal in my attic that I bought months ago on the villages market. The trader claimed to have gotten it off the corpse of a demon after a brother of his killed it when it attacked their caravan. I don't know if his story was true, but I could tell it was magic. After shaping it a little, I found it made the perfect catalyst for linking my soul into the ritual stones.
aw.entries.rituals.waterRitual=Among the first rituals I made with these stones was the one I dubbed "The Ritual of Full Spring". I'll be the first to admit that, in terms of magic, it was nothing special. All it did was create an endless spring of water by using a few tricks from other magics and my soul as a fuel source. But when you are pioneering a new form of magic, you take what you can get. And it was cheap, 500 LP to start it and 25 LP for every bucket of water I got out of it. Luckily I made this ritual before the well outside my home dried up.
aw.entries.rituals.lavaRitual=The next ritual I created was "The Serenade of the Nether", where I tried to make something a bit more useful. I found that the ritual used a large activation cost of 20K LP, to link itself to the nether, then used a much smaller fee of 500 LP to pull a bucket worth of lava to the ritual. I am not sure what part of the nether it is getting the lava from, so it is probably best if we don't think about it too much.
aw.entries.rituals.groveRitual=The next ritual I made was the first I dared to show the villagers. After a horrible harvest due to bugs the villagers didn't have enough food to outlast the winter, and the last of the leaves were falling from the trees. We might have had a rocky relationship, but I wasn't just going to sit back and watch them die. Without enough herba essentia stockpiled to use lamps of growth, I quickly created what I now call the "Ritual of the Green Grove" to mimic its effects. After persuading two of the farmers to at least hear me out, I showed them the ritual and told them what it does. At first they thought I was trying to trick them, but instead of continuing my attempts at convincing them I planted a handful of seeds on the tilled soil I put atop the ritual. Within a minute the wheat was grown to maturity and both of the farmers were awestruck and speechless at what they had just seen. They ran back to the village, fistful of wheat in tow, returning within the hour with a dozen men, bringing their last batch of seeds and half a dozen carts. That evening pushed me to my limits, but we managed to get more than enough food to last the winter and live till the next harvest. And while only the two I first talked to thanked me in person, I knew that all of them were grateful for my help. 
aw.entries.rituals.interdictionRitual=In my studies I heard of an item created by alchemists of yore called the "Interdiction torch". While replicating its effect was tricky, I eventually created the "Interdiction ritual" as a tribute to them. What it does is "Repel" any living thing that gets within its range. After realizing a small error, I changed it to where humans are the sole exception to its effect. I found being flung back by your own ritual has a tendency to make you want to tweak it. While I couldn't make it "free" like the alchemist's of yore, it only costs 1k LP to start and 10 LP per second while it is active. Rather cheap, all things considered.
aw.entries.rituals.containmentRitual=After experimenting with the effects of the interdiction ritual, I decided to try and reverse the polarity.  It created a sort of "vacuum ritual", that I call "Ritual of containment". The cost is the same as the interdiction ritual, due to it using such a similar process. I found this ritual very useful for catching the odd wild rabbit when I wanted a nice dinner. Unfortunately, I caught as many creepers as I have rabbits with it. But a man can try, can't he?
aw.entries.rituals.bindingRitual=Over the years I've heard legends of a place called "Tartarus". Most other mages claim that the nether is what they are referring to, but I'm afraid the nether might just be the front yard while the real Tartarus is something we have never seen. To test my theory, I created a ritual to drag the soul of one of Tartarus's inhabitants to our world, then seal it inside an item. My research showed that few materials had the strength to withstand having a soul bound to them, much less a demon's soul, but I eventually found that diamond was the perfect material for the task. I activated the ritual (needing 5k LP), then dropped the diamond sword I had made years ago on the master ritual stone. Lightning flashed and struck each of the rituals pillars in turn as I quickly jumped to a better observing spot behind a large boulder. When I returned, the sword gleamed red like blood. Once I picked it up it turned into a shapeless goo, but by running a bit of my power through it the goo returned to its normal form. It was far sharper than any blade I had seen before, seemed nigh unbreakable, and as I was testing it on the common monsters that stalk the night I noticed something strange: As I killed them, I would find little red shards dropping form their bodies from time to time. It was rare, but a noticeable change. I made a note to investigate them later as I gathered them. Because of the ritual's function to bring a demon's soul for the sake of tying it to an item, I dubbed this the "Ritual of Binding." Shortly after this, I got my first apprentices in blood magic. Little farmer boys named Fenn and, his older brother, Way Chronos. I showed them whatever I could over five years, then they left to spread the word of blood magic to other mages. They still stop by from time to time, and I am always glad to show them what we have discovered while they were on their travels.  
aw.entries.rituals.beastMode=When I was teaching Way and Fenn we experimented with binding the souls to various items. We found that along with the sword, diamond pickaxes, shovels, and axes could have souls bound to them. All of them needed LP to be used, and they had an interesting distinction to the sword. The seal on the tools could be opened temporarily, for less than the blink of an eye, to unleash a powerful effect. The terrain around the wielder will be destroyed, if the right tool is used (pick for stone, shovel for dirt, axe for wood. Do I really need to spell it out?). This uses a massive 10k LP per use, but the power seems worth the cost. Much later, when I had Tiberius as an apprentice, I did discover that an apprentice blood orb can be turned into the "Energy Blaster", a ranged weapon with the same ability to create blood shards as the bound sword.
aw.entries.rituals.unbindingRitual=The next ritual I created was an idea I had during the few years I taught Way and Fenn. The concept was a ritual that would remove souls from items, namely from the bound tools. Strangely, the souls seemed to become fond of their new homes and really don't want to leave those tools. While lesser souls bound to items as curses by necromancers and the like wither at this ritual's power,  the souls of those who dwell in Tartarus seem far sturdier.  It was over a decade later when Tiberius discovered that it was great at un-binding items from one and other. He used it to when he wanted to experiment with his sigils in bound armor as they can just pop right out using the ritual. It seems it uses 30k LP, regardless of the number of items unbound from the original item dropped in. It also seems as if most items that come from the Ritual of Binding can be thrown into this ritual to recover the original item. And did someone say something about a baked potato?
aw.entries.rituals.jumpRitual=The next ritual I recall making is the "Ritual of the High Jump". It... well... it does exactly what is says on the tin. When activated, any entity on top of the master ritual stone will be flung strait up. After watching a cow use the ritual before me (I had to deal with a mess Fenn made, involving a bottle of ink, Way's hair, some dropped parchment, and a wobbly table. I returned to the ritual just in time to see the poor cow get airborne.), I added a secondary effect where anything that gets flung up can safely land back on the master ritual stone. On the bright side of that incident, we had fresh beef to cook for the next few days and a new batch of ink from some passing traders a week later. I almost forgot to add the cost, 1k LP for activation and around 15 lp per use.
aw.entries.rituals.duskInk=It was at this point that I realized that I was reaching the limits of what I could do with the four inks I had, and spent the remainder of my time teaching Way and Fenn to find a new type of ink. It took four years, but I found it. And it was worth every second. By mixing my blood and Mana with a coal block I created a scribing tool that turn blood into an ink so dark it seems to absorb light. I went on to call this new scribing tool "Dusk" as a result, and rituals far more powerful became possible.
aw.entries.rituals.magnetismRitual=The first ritual I made after creating that ink, strangely enough, didn't even need it. I had such a rush of new ideas that I didn't care if it used the dusk ink or not. Anyway, I based this ritual off of a small stone I saw the children of the village play with that had magnetic properties. By creating a ritual that mimics this attraction, I made the "Ritual of Magnetism" to pull ores out of the ground and place them in a three by three by three meter cube at the heart of the ritual. It seemed to pull from a seven  by seven meter area centered on the master ritual stone, and I managed to tweak it to pull up almost any ore, metallic or not. I offered to help the miners in town with this, but they didn't want anything to do with my work. After realizing I forgot the cost again, I started leaving spaces at the end of these entries for them. This ritual has an activation cost of 5K, with 50 LP for every ore pulled up.
aw.entries.rituals.crusherRitual=To pair with the ritual of magnetism, I created the "Ritual of the Crusher." It will break any block in a three by three by three meter cube below the master ritual stone. Instead of having the crushed blocks fly off, I made the ritual push them up into an inventory above the master ritual stone. It doesn't really care where you want it, whether it be a simple chest or a barrel or something else. 2.5K activation cost, 7 LP per block broken.
aw.entries.rituals.speedRitual=As a variation of the "Ritual of the high Jump", I created the "Ritual of Speed". It was a simple task to modify an effect I already knew well to fling things at an angle instead of just up. What must be stated is that the direction, where the dusk ritual stone is, is the direction the ritual will send you, at a very rapid rate. You may want to have a way to slow down, as I discovered the hard way. 2k activation, 15LP per use. 
aw.entries.rituals.shepherdRitual=Remembering the success of the ritual of the green grove, I wanted to test myself by creating a ritual that could transfer the effect from plants to animals. The "Ritual of the Shepherd" was just that. Any animal in the ritual's area of effect will grow at an alarming rate, what would take weeks just took an hour. After several tests, I concluded that if an animal took twenty minutes to grow from an infant to an adult, this ritual would cut that time down to three minutes flat. So far, I have found no side effects of using this process on animals, and I hope this remains the case as time goes on. 10K activation, 2LP every time it speeds up growth (about once every second).
aw.entries.rituals.darkMagic=The next ritual I created was one I'm not proud of. But after over three months of constant bandit attacks on the trade routes around the village I decided to take action, and anything I had at hand was far too merciful. They had earned a slow, painful end for all their crimes and I intended to deliver it. I created a ritual to use as a trap, laying it just off one of the largest trading routes, behind a thick group of trees. I then dressed as a merchant and bought a horse and cart to lure the bandits out. Like moths to a flame they came for me, riding on horses of their own. They expected me to either surrender or stop and put up a fight, so they only sent out two from their group behind the tree line. Once they saw I had no intention of stopping, they all gave chase, thinking I had something of great value, worth running from bandits for. I turned a tight corner into the tree line, the cart beginning to tip as I did, and went off the road to where I had set up the ritual overnight. By the time they saw it, it was too late for them. I stopped at the base of the ritual, knowing they couldn't get away in time. "What is this, where is your treasurer?" one of the first bandits there said. "I thought you had something, but now you stopped." I gave him a bitter laugh, he's more concerned about looting me than the reason why I brought them here. "I don't have any treasurer on me or in here, I came out here for you." By then all twenty of the bandits had arrived, some filled with blood lust, others full of greed. "Then why did you run? What is that thing anyway?" By now the head bandit was straining to keep ahold on his sword, so I kept him taking. "Oh, nothing much. Just a ritual built by a blood mage. I wanted to make sure and bring you here, just to test it out." Now all of them were pale, and their leader was having a hard time staying on his horse. "Blood magic? Are you the mage who built this? What are you trying to do to us?" He bellowed as he was trying to turn fear into anger. He was getting nervous now. Good to see the title "Mage" still sends a little fear into scum like him. "Nothing much. Well, to me anyway. To someone like you, who has cast aside his humanity for a quick bit of coin? A slow and painful death."  At that point, most of the bandits were already lying on the ground while their horses started backing out of the clearing due to the panic and confusion. "Why... Why are you doing this?" The leader managed to spit out with his face in the dirt and far too weak to stand.  "Because I have heard what you have done. How many you have killed for the few coins in their sacks. I know that you are slowly killing the village I call home, and while I am not someone who they like having around just outside their homes, I still feel obliged to help save them from any mortal danger they might face." At that point, none of them could move. After about five minutes, the last of their life essence had drained away. I tore the ritual apart, then went back to the village with all of the bandits' horses. I gave them and the cart to the "Mayor" of the village, if you'd call him that. He pulled me back into his office and asked where I got them from. I told him the truth, part of it anyways: "It seems like those bandits we've had for the last few months earned the ire of the wrong mage." He didn't ask any further questions, nor did he ever bring up the topic again. All I know is that I found a large pile of gifts from the village on my doorstep the next day, from fresh bread and milk to fine fabric. After a few rumors spread about what happened, we never has bandit troubles again. 
aw.entries.rituals.knifeAndSufferingRitual.1=Years later when Tiberius created his blood altar I split that ritual into two versions, the "Well of Suffering" and the "Ritual of the Feathered Knife". Each needs a large activation cost of 50K LP, and I tweaked them so that they can fill a nearby blood altar with the essence they drain. Each has safeguards so that what happened with the bandits won't happen again. The well of suffering cannot affect humans, and will slowly kill everything inside its range. It will drain life one half a heart at a time at the cost of 2LP every time it steals essence, filling the linked altar with 10LP. I have tuned it to work with the altars sacrifice runes, so it is highly recommended to use them to boost the LP output of this ritual.
aw.entries.rituals.knifeAndSufferingRitual.2=The feathered knife on the other hand uses the life force of humans to fill the altar, but I have it set to stop when they start feeling weak, at about three hearts. Like the well, runes can boost its output of 100LP per half heart, but it needs self-sacrifice runes instead to boost its power. 20LP for every half heart drained.
aw.entries.rituals.regenerationRitual=Sometimes you find the idea for a ritual, and other times the ritual ideas find you. The ritual of regeneration was of the latter group. I was going about my daily business when I heard frantic knocking on my door, it is a rare thing for someone to come to my house willingly, let alone in such a panic. So I rushed to the door and found a few of the villagers standing there, one of them holding a boy of about ten in his arms. The boy was wounded badly, several deep cuts and dark bruises covered his body. There was enough blood on his clothes that I first thought them originally red. They told me that traders rushed him here from a nearby village that was under attack by demons, and that their healers couldn't do anything for him. After a quick glance, I told them to bring him inside, then sent them home. I did what I could to hold off infection and slow the bleeding, but nothing I had on such short notice could help more than just delaying the inevitable. But I couldn't let it rest at that; not having anything on hand was never an excuse for giving up. I hastily threw together a ritual, mixing concepts and taking risks to build it. Still untested, I brought the boy to the ritual. At first I thought nothing was happening, then after a minute I noticed his wounds starting to close and the bruises beginning to fade you can't imagine how glad I was that it worked. As I started gathering and preparing more medical supplies, I noticed the boy had opened his eyes. He was still quite dazed from the blood loss, and he was looking at the ritual with nothing less than awe. I caught his attention and gave him a light smile, holding a finger up to my lips as the universal sign for a secret. This was the first time someone, who wasn't one of my apprentices, got to see my lab, and there were quite a few thing here that I'd rather have the villagers not know about. At that point they had seen a few of my rituals, but they didn't know what fueled them. I then used a simple spell to send him back to sleep. I later found that my hastily assembled ritual had an unintended side effect: it didn't just work on humans: anything, from sheep to creepers, inside its area of effect will slowly be healed. I also discovered that it slots in easily with the well of suffering and the feathered knife. It needs 5K to activate, with 200LP every time it heals a human (It seems our lives are worth more than cows or creepers.), and 20LP when it heals anything else. I later learned that boy's name was Tiberius, and he grew curious about my work. For six years I held off his demands to train him. It was only after he mention seeing red threads coming from me and being attached to every ritual and item I had used blood magic to create, that I considered training him. It was only after he wrote down a theory and showed it to me that I finally said yes. I had him learn thaumaturgy as a cover up story for the villagers, and as a way to show him the difference between blood magic and other magics. First thing he started working on was an improved way to use blood alchemy, rather than learning the way I had been doing it for decades. I had to laugh to myself when he first told me that I had been so busy creating rituals that I never stopped long enough to think about ways to improve the other aspects of blood magic. So taking him under my wing was a good decision, even if he left me little choice.  The blood altar we finally created was a much more efficient way to use blood alchemy, needing little in the way of magical prowess, unlike the way I had been doing it, while also being slightly cheaper in terms of blood use. We also created what he dubbed the "Blood orb", a vessel meant to mimic the soul while acting as an extension to it. After finishing the altar design it took him one week to build the altar and create the orb, granting him the same soul network capacity as two weeks of meditation. I might need to rethink the way I use blood magic, considering the leaps and bounds he is making with it. I might also be better off sticking with rituals and let him re-invent the wheel, since it seems like he is adding a few wheels of his own after I found him sleeping at his work desk drawing up a plan for a portable ritual. He soon created items he calls "Sigils" as a way to use similar power to rituals, but more portable and most are intended to be used on or around the caster. He quickly created a sigil meant to read the capacity of the bound user's soul, but when he tried to read mine I repelled his efforts. My way might have been harder, but it has given me a few tricks he might never learn.
aw.entries.rituals.harvestFestival=With Tiberius busy with his own work, I decided to challenge myself. I wanted to start creating rituals that pushed myself to the limits of what is possible with blood magic. Unfortunately, there was something baring my attempts to create these rituals: My activation crystal. It was barely able to conduct the Life essence required to activate rituals like regeneration, adding any more than that and I might lose more than just the crystal. I wanted to start researching a way to improve my crystal, but my duties to the village came first. We had a small drought to deal with, a small taint problem to the north, and the Hell's harvest festival was coming up, and so I had to meet with the other Conglomeration members to give updates on my work. I had grown to detest some of the work they do, but I still remain with them as their oldest member and as the last conglomeration founder still alive. Not that I had much choice when I joined, it was ether sit through a somewhat dull meeting every year and earn some gold for not causing trouble over the last year or let them destroy each other in an all-out war for the nether and its resources.  There are still days when I wish that I went for the latter option. I asked Tiberius to help the villagers while I deal with the taint. On my way to the taint site it started to rain, so that might have made Tiberius's life a little easier. The festival came quickly, and I did my part in the opening ceremony and watched as Tiberius lit the portal. Never understood why we go through all the hassle of lighting the portal every year to then close it three days later. Regardless, we all entered the nether to see the others, already at work. I wandered off to greet people and answer a few questions, leaving Tiberius to fend for himself. I glanced back at one point to see him with a face that could only be described as pure disgust. Apparently he feels the same way I do about what the nether has become. I still remember the early days when I first opened the nether portals, back when the pigmen roamed wild and free, and death was around every corner. Those days seem like a dream now, after everything the Conglomeration has done to industrialize the nether to maximize their profit margins. Later that day I decided to visit the small building I own here and sleep at during these festivals. I found a note on my bed from Tiberius saying that he had some work he wanted to do, so he went home early. Can't blame him, I would have left myself if I had the choice.  I went to this year's meeting spot late in the evening, when fewer people walked the paths cut into the netherrack. I attended the festival every year, so most people were used to seeing me here and I even got a few greetings from passersby. I walked towards the huge building carved into the netherrack with "Thermal expansion: Powering the world!" inscribed on the front of an otherwise purely white tower. I walked through the clear glass doors, then started my way towards the elevator. A tall skinny man stood inside the metallic box, and said "Greetings sir, where would you like to go?" as I walked inside the elevator. I had never seen him before, so I already knew what would happen next. "I need to go the thirty fifth floor. I have a meeting to attend to." I recall saying to him.  "I'm sorry sir, but I can't do that. Only V.I.P's are allowed on that floor." I sighed as he was beginning to try my patience. "I know that, look up the name "Magus Arcana" in your little terminal over there." At first he seemed reluctant to even check, but once he saw I wasn't leaving till he at least looked at the terminal he turned and quickly hit a few keys. After about five seconds of reading the data that came up he quickly spun back to face me, his face a few shades paler. "I'm so sorry sir, I didn't know that..." I quickly cut off his stutter at that point. "No need to worry, I'm used to this by now. If we can start heading up now I might even give you a tip." After that he closed the doors to the elevator, pulled a key out of his pocket and stuck it into a small hole in the wall. A piece of the wall slid open and I saw the scanner that was hidden behind it, then he brought his eye up to it and asked me to do the same. Once it had scanned both our eyes it gave a small chime to let us know we did everything correctly, like it had the last time I had been there.  I gave the man three gold coins, a generous tip to a king, let alone him. He could feed himself for a few months off that alone, and he might need to after today. They rarely keep their jobs if any of the board members didn't like them. I still heard him thanking me until after I entered the meeting hall, everyone else was already there. I knew almost everyone present, with only one new face at the seat of GregTech Intergalactical. They almost always had a new guy attend each year, so I assumed the Gregtech company was as cutthroat and brutal as rumors said. "Now that everyone is here, shall we get started?" Iza Lemming, daughter of thermal expansions founder asked once I took my seat. The new guy jumped up and started yelling once she said that "Wait, who's he? I don't recall ever seeing him before!"  "Calm down, Frank. He is the guy we've been telling you about for the last half hour." Iza said. I couldn't help but crack a smile as she held up her reputation for not having any time for idiots. "Wait... He is the guy who lit the first nether portals? He is one of the founders?" I could tell he was confused, and I couldn't blame him. The fact that it was me who lit the portals a century ago is one of my best keep secrets, and the fact that I'm on this conglomeration's board is something only a handful of people outside this room know. While in truth I didn't discover the nether, I was the one who popularized it by creating portals in many major cities. "Yes, that is him," Iza said as I sat next to her "Now that you're up to speed will you take your seat so we can get started?" He took his seat again, but his gaze never left me. I looked around the table at everyone, noticing once again that for another year none of the other Conglomeration members bothered to show up in person, aside from Iza who owned the building we all sat in, opting instead to send representatives on their behalf.  This year's meeting went on like every other years'. Trade requests and action requests were announced and Iza requested permission for two wither summons to start up a nether star generator. Frank demanded trade from me for iron and iridium, claiming that I owned a huge mine full of both. After ten minutes of trying to calmly tell him I owned no such mine, Iza told him to sit back down and drop the request or leave the room. He returned to his seat quickly, but I still saw the steam coming from his hairpiece that had come lose in his fury. No one bothered to tell him about it. On a quick note: I was telling him the truth, I own no such mine. I sold it a few years back, and only get a small cut of anything mined. But he didn't need to know that.  The other requests went much faster, with Industrial Craft asking for some of Minefactory's rubber supply after a bad harvest, Mechanism requesting use of an ender quarry, and forestry asking for permission to search my lands for their precious bees. I gave them permission, as long as they sent a few traders through with a few of their products like honey and waxes. Then we moved on to general business of who earned how much, how far in the black they are, who now runs what. I sat quietly through all of it, making mental notes of all this information. As we were about to move on to the final topic, Frank decided to interrupt. "Wait, we haven't heard from Magus yet. Please, tell us what it is that you do? I honestly never heard a thing about you until today, and I see no reason why you own such a large area of land. I am even more curious about why you're the only magic user here. Please, enlighten me." He had a cocky grin on his face, like he had just asked questions I had never heard before.  I gave him a very unamused response. "I'm sure you have heard of me, just not by name. My name is more in rumors and legends than in newspapers, and I honestly prefer it that way. As for what I do with my time, I spend it looking into legends and myths, old tomes and texts for information about new or lost forms of magic. You have huge R & D departments, I have a few friends who share their research and knowledge with me, and apprentices that always find new ways to use old things. As for why I'm the only magic user here and why I govern such a large area of land, the answer is simple: The mages never split up. While you technology users divided your land among over a dozen companies, the mages never did. We're happy governing ourselves, and we have never needed corporations or rulers to tell us what to do and what not to do. If you put the land together that all of you own, then compare it to what I have, you'll see that I have less than a third of that. Not my fault you split, and because I don't really "rule" my area they have no one to divide from. Does that answer all your questions?" He looked like I just popped his favorite balloon, so I guess he assumed to have me pinned. "But....but... That still doesn't answer why you haven't been at war with each other! I know there are many kinds of mages, so there is bound to be some in-fighting." I smiled, he used a question everyone like him has. "You're right. There are many kinds of magic, and there are small battles from time to time. But all the mages know that this is the only place where they can practice magic freely, in broad daylight. When the world is against us, we can set aside our differences to work together. Is that enough of an answer for you?" He apparently ran out of questions, as he grumbled to himself then waved to have the meeting move on. An hour later most of the representatives had left, first and foremost Frank. Only Iza, John.W. Tema from Extra Utilities, Prince Muse from the power suit empire, and myself remained to chat informally. Iza had decided to talk to me for a bit, as it was customary for the host to be the last to leave.  "So, what did you think of Frank?" She asked, a light grin on her face. Nice to see running a company like this hasn't gotten rid of her humor. "Well, I don't think he likes me. I have no idea why." She gave a quick snort, trying hard to hide it. "It's how men like him are. He knew all kinds of information about all of us when he got here. That fact that he knew absolutely nothing about you was driving him nuts. He first thought it was a mistake when he noticed the number of chairs, and asked to have one taken outside. Let's just say I'll be happy when he is replaced, and I won't miss him at all." "Let's see...is he the fourth or the fifth to try and ask me questions they think I won't answer..." I said almost to myself, trying to remember.  "Neither. He was the sixth, if my memory severs me," John said, Muse having left the room while we were talking. "I think you're right, the last one was about five years ago. Rodger, wasn't it?" I chuckled as I remembered him. "He demanded to see my papers, and thought we were trying to trick him. He never did believe that I own the largest chunk of the world. "  "Well, you less "Own" it and more guard it. You tend to let the people own themselves, for better or worse." John said, taking a seat closer to Iza and me.  "True. And it works for the better more often than not. Besides, I never could see myself in one of those big buildings you are both so fond of. Too flashy for my taste, I prefer to blend in, not stand out." I told them.  "That's why you live like a hermit." Iza said, before leaning back in her chair. "I admit, I envy you at times, Magus. You can walk amongst your people and they will give you little more than a wave. If I try to do that, an army of news reporters track my every move in the hopes I slip up. It's exhausting at times." John gave a small sigh "I can agree with you there. Everyone I work with wants my job. I keep telling them that they really don't."  We chatted like this for another hour, both of them letting their hair down as they talked about what's been going on with their lives. We left, Iza to the pent house on the top floor and John joined me on a short walk as I returned to my home in the nether. "I must say Magus, I always enjoy the end of these meetings when we can put business aside and actually talk for a change. Paper work is all well and good, but it is nothing compared to having a nice chat with you. I grow sick of all these "Yes Men" who only want to tell us what I want to hear till the ship starts sinking." "Having a real conversation with you and a few of the others is why I stay on the board. Everything else grows worse every year, but the afterward is still worth it all. How's your wife doing anyway?"  He gave a quick chuckle before saying, "You know, I've been on that board for the last ten years. You're the only one who ever bothered to ask about my family. She's doing just fine, caring for our daughter while I'm out this weekend." "Well, we only have to work today. Since we already wrapped up business you might want to spend the next few days with them. If anyone asks I'll tell them you had something more important to do." He placed a hand on my shoulder. "Thanks, I might hold you to that. It has been a while since I gave her a nice surprise. I'll see you next year." He said as he started walking to a hotel. "Feel free to visit my home any time, I'm sure your daughter will enjoy a nice vacation." I told him as he left. He gave me a nod and a grin, before turning back to the path, almost running into a lamp post. The next two days went by much faster. I greeted people, helped a few fellow mages with questions. Apparently Way and Fenn had already started spreading the word, a few asked how my new research project was going. A few other Conglomeration members stopped by my small home, wanting to chat. I didn't see Frank again after he left the meeting, so I assumed he went back home. After the festival was over, I did my normal part in the ceremony by watching them close the portal again and not demanding that they leave it open. I returned to my home to find that Tiberius had been busy while I was out. He had created runes to augment his altar. When I asked him what purpose they had, he said the altar could make a few things that were not possible before, but that was about it. The "Blood Runes" he created looked a little plain, but I didn't voice my concern. As I fell asleep that night I realized how much they look like my ritual stones. Perhaps painting them might give them more power, like they do in my rituals? I was right, as Tiberius soon discovered accidentally. He managed to create runes on the stone using sugar, which made his altar work a little faster. We also noticed that the altar only had enough power to draw effects from the four runes closest to it. Perhaps we need to create something to "Boost" the altar's range... Three days later I heard a weak shout and raced to find Tiberius. He had collapsed at his altar, young fool, after getting far too close to his limit. I helped him stand, then looked to see what it was that pushed him so far: an emerald green blood orb. I gave Tiberius a hand in filling it, and his soul network to 25K LP. He was making quick progress, but I hoped he would be a bit more careful in the future.  Life went on like this for the next year and a half. Tiberius had made several new sigils that gave him a simple form of flight, boosted the speed he worked, helped plants grow, and could remove liquids. We had started work on building a new blood altar behind my home, finding that glowstone worked well enough to boost the altar past the four upgrade slot limit we had reached and that the basement wasn't going to hold a bigger altar. We had gone the extra mile to create runes that boosted how much LP we received from our sacrifices, using the same effect glowstone dust has on the altar. Then one day when I was helping Tiberius create the last few runes, there was a knock on the door.
aw.entries.rituals.thenThereWereFive=Tiberius went to get the door, and I thought it was just one of the blacksmiths asking to use one of the lava crystals he had started renting out. About thirty seconds after he left, he called back. "Master, there are some people here to see you. They're asking for you by name." It was rare enough that we got visitors, let alone ones that want to speak with me and knew my name. I walked to greet whoever was at the door and found three children. One had dark brown hair, a ragged shirt and patchwork pants, an old and somewhat rusted short sword, and looked to be the eldest of the group at fifteen. The second looked enough like the first to be his brother, but not twins as his hair was lighter and he was a little taller. The third was a pale haired girl, so blood relation was probably ruled out. Her hair was long and a little matted, she looked to be the youngest at about thirteen, the dress she had on looked a little too small for her, was falling apart at the seams and it looked like she was trying to hide behind the other two. All three of them were painfully thin, dirty, and the eldest looked like he had a few fresh scars from at least three days ago. "You must be Magus." The dark haired boy said as I walked up. "We've come a long way to meet you. May we come in?" I couldn't turn down anyone who needs help like these three did, so I told them to leave their boots at the door and have a seat.  Long tale short: I now had three more apprentices. Within a week all of them became blood mages after unintentionally finding three new branches of blood magic. Vlad started working with alchemy, both making potions and mixing items to create new items. Demir started working with spells, and has already created a powerful framework for conducting his power. Bella on the other hand has shown us that demons are much more than mindless creatures, and is working out how to form pacts with demons and bring them to our world, body and soul.
aw.entries.rituals.alchemyRitual=I created two more rituals since they have gotten here, one was "The Ballad of Alchemy". It was a way to help Vlad with the creation of large amounts alchemy products, as he mentioned how time consuming creating everything he needed was. After a little work, I had this ritual ready for use. After activation, it needs a little extra set up to work: On one side of the master ritual stone you need to place the alchemical chemistry set, and on the opposite side you need to place an inventory (a chest, or anything else really. Could be a furnace for all the ritual knows or cares.) Then the remaining two sides are "Input" sides, where the ritual will search any inventory for items. On top of the master stone you must place a blood altar with an item inside to act as a "Focus". The ritual will then pull items out of the input sides and place them inside the chemistry set, then any item it finds in the output slot, or any item not needed in the crafting (such as empty buckets) and places them inside the output inventory. Once I showed this to Vlad he nearly passed out, then hugged me. To say he was happy is an understatement. 20K activation, and 10 LP every time it moves an item.
aw.entries.rituals.domeRitual=The second ritual I created was the ritual of the dome. What it does is simple, it removes any fluid block in its radius of ten blocks. But it doesn't just remove the fluid, it stores it in a small pocket of space so that when it is deactivated any blocks it removed will return. Maintaining this effect does have a small LP cost every tick. While the range seems small at first, I am sure that I will be able to find a way to extend it. Placing stuff underneath the ritual appears to have no effect, so I should ask Vlad or Tiberius to look into it. Activation cost of 10K, with a small passive drain. Hmm, to think this ritual came about when I had to deal with a small flood... That brings us up to present day. I am still trying to strengthen the activation crystal, while all my students are busy with their own tasks. I will write again once I have something worth writing about happens. 
aw.entries.rituals.awakenedCrystal=After a little over three years with having Vlad, Demir, and Bella as apprentices, they have helped me awaken the true potential of my activation crystal. After helping Bella create a way to summon demons, she showed me an item a special demon called an "Elemental" drops: The demon blood shard. I already knew that the activation crystal seemed to be attracted to blood shards, but what we have been harvesting off common monsters of the night turn to dust when I try using them on the crystal. But this... this blood shard has a slight demonic aura to it, and it seems like it wants to merge with my activation crystal. After some work with Vlad, we used a few of his more potent alchemical reagents to bind the demon blood shard to my activation crystal, "awakening" the true potential of it. I have yet to try and build any new rituals, but that will soon change.
aw.entries.rituals.featheredEarthRitual=The first ritual I created is "Ritual of the feathered earth". It has a huge LP cost of 100K, but any fall damage within a 30 block horizontal range or a 20 block vertical range is negated because the ritual turns the ground soft. The ritual needs no further cost after activation due to the semi-permanent nature of softening the ground. I was trying to make a ritual to let me fly, but this seems useful as well.
aw.entries.rituals.gaiaRitual=The next ritual was based off an old magic ritual that required three royal goat sacrifices. I have managed to replicate the effect using blood magic, and have named it the "Ritual of Gaia's Transformation." The basics are this: By sacrificing items and 1M LP to Gaia, you can change the climate (or biome, as some call it) of the area around the ritual. Unfortunately, Gaia will only take items off arcane plinths around the master ritual stone, but nothing else about this ritual is cheap so why should this be? The rituals area of effect must be defined by using blood stone bricks (either the larger or normal, and has a range of 21 blocks) and it only effects places with a definable path to the master ritual stone. Simply put: If you placed a water block on top of the master ritual stone, then placed more water blocks where the water flowed until the water stopped flowing, where there is water is where the ritual will effect.   The ritual assumes first of all that you have a humidity of 0.5 and a temperature of 0.5. It will then consume the items that you have placed and modify the humidity and temperature accordingly. It also starts with assuming that a range of 0.1 is acceptable in both directions. If the ritual cannot find a biome that is registered in the list, it will simply set the biome to a "plains" biome, with a biome ID of 1. The items that can modify these parameters are as follows: Sand: humidity - 0.1 Sandstone: humidity - 0.2 Netherrack: humidity - 0.4 Lapis: humidity + 0.1 Water bucket: humidity + 0.2 Lapis block: humidity + 0.4 Coal: temperature + 0.1 Coal block: temperature + 0.2 Lava bucket: temperature + 0.4 Snow ball: temperature - 0.1 Snow block: temperature - 0.2 Ice block: temperature - 0.4. It is a tricky ritual to use, but the local bee keepers are thrilled by the possibilities. A tundra at the heart of a dessert, mushroom biomes anywhere, or even tweaking the Nets biomes so living there is possible.
aw.entries.rituals.condorRitual=After three month worth of work (Tiberius says I'm obsessive about rituals, and I have little ground to say otherwise. I worked on building this ritual for three days strait, without eating or sleeping.), The Reverence of the Condor (named after a legendary Thaumaturge's sword) allows anyone in a ten block horizontal range (from bedrock to skies limit, only horizontal distance matters.) will be able to fly freely. This ritual needs 1M LP to activate, but has no passive cost at all. Since adding this ritual to a new basement layer, I have found it to be useful. Caution mast be taken however, as this ritual does little for fall damage. It is recommended to use this with the ritual of feathered earth for best effect if you are planning to stray from its area of influence.
aw.entries.rituals.meteorRitual=After reading an interesting book on where ores come from, I built "The Mark of the Falling Tower." It uses a process similar to the ritual of magnetism, but instead of pulling them out of the earth in drags a meteor out of space and pulls it toward the master ritual stone. (Note: The meteors summoned by the ritual often come in with a powerful blast. This is unavoidable, as we are dragging in huge chunks of rock at very high speeds.) This process is not cheap, needing 1M LP to activate, then a "Template" item sacrificed by dropping it on the master ritual stone to help the ritual find a meteor. The item dropped will influence the meteor it finds, but there will always be some randomness to the process. Here is what I have discovered with some experimentation (Note, this might differ from person to person. I can only give you a reference point for what you might get and tell you what they need.): A stone block summons a huge meteor filled with lots of stone, while also having ores often found close to the surface of the world scattered about. Common things to find are: Coal, apatite, and iron. An iron block summons a meteor filled with metals like iron, gold, copper, tin, lead and silver. It also has very little stone, and some lapis and redstone mixed in with the metals.  A diamond calls down a meteor that is often filled with gems (diamond, emeralds, amber) and other rare items like cinnabar. Finally a nether star calls down a meteor filled with very rare or tricky to get ores like nether quarts, diamonds and emeralds (more than what the diamond summons), sunstone, moonstone, certus quartz, and the always rare iridium. (Gregtech has become a lot friendlier since I discovered this ritual, for some reason). I should repeat that your location in the universe influences what the meteors come with. The ore amounts, meteor size, and even what ores do spawn have been known to change, from some having metals I've never heard of to other iron block summons having only iron, gold, lapis and redstone. It is almost as if someone can tweak these factors to their heart's content... No, nothing more than my paranoia. 
aw.entries.rituals.expulsionRitual=The next ritual I have created with my wonderful new activation crystal was one I'm rather proud of. Every few years someone gets it in their head I'm the incarnation of evil and then raise up a mob to try and kill me. Since I knew it was time for it to happen again (always just after the first harvest, when the farmers are planting more seeds. This is around the time it happens, like clockwork.), I created the aura of expulsion. What it does is simple: It creates a field in a square 25 block radius that will check for anyone who isn't the activator. If it finds someone who isn't the activator, it will "Expel" them by randomly teleporting them away. This ritual has an activation cost of 1M LP, with a 1K fee every time it expels someone. I should note that if someone lands inside the field of another aura of expulsion they will be teleported again and again until they land outside the field or the ritual owner's network becomes dry. You have no idea how entertaining it was to sit on my porch and watch as the mob suddenly realized that they were heading in the wrong direction when they started marching toward my home. It took them an hour, but they finally figured out that they couldn't get close to my home. Afterward I got to hear all the silly reasons why they wanted to kill me this time. Also, I have added a check to the ritual for an inventory above the master ritual stone. If it finds a blood orb bound to someone inside, then they become "Whitelisted" from the effect of the ritual.
aw.entries.rituals.costOfProgress.1=Well, I write this with both good news and bad news: The good news is that we now have a lead on what my students need for their next tier of blood altar. The bad news is that what they need is very hard to get: Beacons. Four, to be exact. Nether stars are an extremely rare resource that very few have access to, so building this seemed impossible to them. I told them that I might be able to pull a few strings, but it will take time. For now they are working on building the upgrade runes they will need, then I will tell them to try and perfect what they can do with their master blood orbs. Thankfully, the next Hell harvest is in less than a month so I can start the plan I have been thinking up soon. But it will take at least a year to accomplish, so we have time to kill... I have just returned from the Conglomeration meeting, and my plan is now in action. You see, the host for the next meeting is the Mage guild, and as the host I have the authority to make a request: That all the Conglomeration members show up, in person. It has been what, thirty years since I have met most of them? Regardless, I gave each of the representatives a letter to deliver to the Conglomeration board members that said "As the host of next year's meeting, I have two requests. The first is that we hold the meeting near my home instead of in the nether, as I really don't have anywhere large enough to host there. The second is that all Conglomeration members show up in person. Don't bother bringing servants, but you may bring friends (give those poor souls you send in your place all the time a thank you. They deserve one.), and family members. Room and board should not be a problem, as I will gladly provide both. Don't send people in your place like most of you do, as I will not allow any substitutions.  "If you attend this meeting, guests or no guests, I have prepared little gifts for each of you as a thank you. Inside this envelope you will find a card with what I am offering. You may find my offer beyond believe, but I can assure you that I have everything offered and more. Consider this a little incentive to come, as I know most of you spend the Hell's harvest working instead of enjoying a small vacation.  If you wish to attend, simply send a reply letter to me in whatever manner you see fit. I look forward to seeing you all again." The gift I offered depended on who got the letter. To Gregorius Tech IV (he doesn't trust anyone else to run his company, so every time he feels like he can't run the business anymore he makes a clone of himself, then moves all his memories to the clone.), I offered a stack of raw iridium ore. To Iza Lemming I offered a few resonant ender cells (fully charged, of course. I have more than magic tools in my basements.). I could list them all, but then this book will drag on longer than it already has. I told my apprentices that I should be getting a few letters soon, and a week later I started getting the letters in a wide variety of ways. Gregorius opened a hole in space to have an intern hand deliver it, Queen Muse sent a power armored messenger with her letter soaring over the tree tops. Lucas Jaguar sent one of his personal trains in, one that built a track as it went and removed it automatically. I could go on, but I think you get the idea. When Tiberius asked what was going on, I told him I would be having a few guest over in a few months and these were their answers to my invitation. All of the letters said yes, so I must have gotten their attention.  For the nine months after the Hell's harvest festival, I learned all I could from my apprentices. I wanted to use Tiberius's artifacts, so he showed me every sigil he has made and helped me upgrade the bound armor he gave me. Vlad showed me the secrets (in exchange for a few secrets of my own) of his potions, and the power he could bring out of what used to be so simple. Demir gave me spell components, and showed me how to build a spell structures. Bella helped me summon and tame a demon similar to Sinferrius (the "Fallen Angel" breed), who was named Thanatos. We found that the two of us shared a thirst for knowledge, and he started helping me with research in my library. When there was only three months until the next Hell's harvest festival, I opened the "Conglomeration Hall."
aw.entries.rituals.costOfProgress.2=The Hall is where I have stored most of the Technology I acquired over the years, from trades with the other board members or deals with more shady groups. Every innovation and project that any of the world's companies have ever made can be found here, from redstone furnaces to industrial grinders, power armor upgrades to quantum armor, and coal coke ovens to mass fabricators. Most of the time I never use this place, but it works as a fine testing ground for applications of my magic. After doing the ritualistic "Dance of the Mage" to call up from the ground then open the gates to The Hall, I noticed that all of my apprentices were standing behind me slack jawed. "What... What did you just do?" Demir finally asked. "I opened the door to this old place. We need to do some cleaning if we're going to have guests here. Dust gets everywhere and... Well, what are you waiting for, come in and help me!" It took all of us the first two months to clean the place (all ninety-five floors), and we spent the final month taking care of all the last minute touches such as making a large order for food from the great meat and produce alliance (Queen Pam, Harvey Growth, and Larry Iguana are the main representative for the Conglomeration, but there are many smaller colonies that make up their territories). Shortly after making sure everything was in working order, all the dust was cleaned out of the place, all the beds had clean sheets, and many other tasks were done was it time for the Hell's harvest festival, I asked my apprentice's to watch the place while I lead the other members here, and to greet them when they arrive. I think at that point Demir still thought this was some sort of prank, as there isn't any mention of the mage guild having a representative in any history books (and I've made sure it stayed that way. Any who have tried find their books missing, pages torn out, and critique for believing some legend is fact.). We went through the normal procedure, a merchant that had just moved in to town lighting the portal. After milling about the place for an hour, I met all my guests at Fire Grove Park (tourist spot, if you can believe it) and set about escorting them to my home as orderly as possible (which wasn't very). Thankfully, almost everyone was in commoner clothing as I had requested so no one realized all of the leaders of the world were walking past them. After just half an hour, we were all in the carriages I rented (I told the merchant it was a family reunion. He never asked if it was my family.), and on our way to my home. In total there was a little over a hundred and fifty people here, so I'm glad they took up my offer for guests.  Once all of the carriages arrived, and I had asked the drivers to return a few days later, I set about greeting everyone and showing them around. It took most of the day, but everyone started relaxing and enjoying a nice vacation. We all came together again for dinner, and as we enjoyed meals cooked by a local family run restaurant that I always hired for large meeting like this I moved from table to table, greeting guests personally and answering questions. As the hour grew late all the Conglomeration members joined me in a small room for the meeting.  "Well Magus, I had no idea you owned a place like this!" Raylind Diyo said as he came in. "I might have paid you a visit sooner if I did." Raylind said as he took his seat, smile on his face. I saw he brought both his wife and daughter here, so that might be why he is in such a good mood. "This place is better than any hotel or amusement park I've even been in!" Carrin Calclavia, the fifteen year old owner, head of research for Modular Force Fields, and daughter of the former owner Count Calclavia said while spinning in her chair. "Seems like a waste of my time," Gregorius said. "Oh, don't be such a spoil sport Greg. Most of us have forgotten what not working every minute of our lives is like, and I for one remember why I liked not working when I was younger!" Jason Algorithm of Applied Energetics said as he took his seat next to Greg. Greg gave him a cold glare for a few seconds, then his expression broke as he said, "Alright, I'll admit I have enjoyed not worrying about paper work for the last few hours." "Now that Greg has almost smiled, let's go to Magus for more miracles," Carrin said, mischievous grin on her face. "Well, everyone is here, so we may start. Any news to share?" The news this year was a little more lively than normal, a few had started "Re-inventing the wheel" to use their technologies in new ways, while a few others had started work on new projects. Then we moved on to the normal trade requests and protocol stage, where it was business as usual. Until it was my turn. "Before we move on, I have to request a few wither summons and kills." I told them. "Ah, you need nether stars? I still have a few spare, so you can use them." Jason said. "No, I only need a few stars. It is the killing of the Wither I'm more interested in. Call it a test for my students, who I'm sure most of you have met earlier today." Raylind started brushing his chin with one finger, before saying "Really, you want to have those kids kill a Wither? Seems like a bad idea to me..." I smiled at this "Don't worry about them, they can handle themselves. It's the withers you should be worried about."  "Besides..." Carrin said "All of them are older than me. So I would not call them kids. And they're more mature than a few of you in here..." I had seen Carrin and Bella talking earlier, so it isn't a surprise she is defending them. And Carrin loves getting gabs in when she can, so this was the best of both for her. "We're getting off topic. How many wither summons are you requesting, and how many do you plan to have done by hand?" Iza Lemming said, trying to help poor Raylind.  "Thirty summons, and thirty manual kills."  This drew a gasp from everyone, including Greg. "Thirty... All at once?" Doland Tema, son of the founder R.W. Tema asked. "No. one for each my students, six for myself, and twenty as a finally. As I said, thirty," I told him.  "Might I ask what you need that many nether stars for?" Greg asked, eyebrow raised slightly. "As I told you, this is a test for my students and myself. We really need only four stars, but I'd like to test the limits of the new form of magic we've been working on. And if you don't mind, Doland, I'd like to use Hell's Fridge for all of the Withers." He looked at me for a few seconds before saying "Well, it would be a shame to kill all these withers without making a show of it. Alright, you helped build the place anyway so I have no reason to deny you. Hell's Fridge is yours to use. And I approve the Wither summon request." One by one everyone approved the request, some more warily then others. Everyone here knew what nether stars can do, so someone with thirty of them� Even in my KAMI days I didn't ask for that many at once.  The meeting ended shortly after that, but I asked Greg to stay as I had to talk with him privately. After everyone else had left, I created a few protective wards to keep what was said secret to any eavesdroppers. "What do you need to talk about?" Greg asked, more curious than anything else. "I know we're both tired, so I'll get right to the point: I know all about your little "Project New Gate."" This seemed to shock him briefly, so I said "Oh, you and I both know your company can't keep secrets. It would have gotten out sooner if I didn't pull a few strings to keep it under wraps." Greg gave me a hard look for a few seconds, then sighed. "Alright, how much do you know?" "Everything." I told him. "I know you opened up a gateway to Tartarus, and then you started using robots to mine the place for new materials. But that didn't quite go to plan and demons overran the facility, then started getting out and roaming the land. That was about twenty years ago." Greg slumped his shoulders, knowing I had every detail about one of his darker secrets. "So what now? You're going to tell the others? Try to bankrupt me or black mail me?" He said, voice filled with regret. "No, as this wasn't your fault. You couldn't have ever known something like this would happen, from what I've heard and seen. It was an accident that has gotten way out of hand, to the point where you can't clean it up, and your pride has stopped you from asking for help. I know your company caused this problem, and for the last twenty years you've been trying to fix it. So now here is what's going to happen: You are going to talk to all of my students and tell them everything you know. Tomorrow we tell the other board members. Then, once my students tests are over with, you'll send me all the data from your research notes on New Gate. Tartarus is a place filled with both demons and magic, so I should have a better chance of opening a stable portal that I can enter Tartarus and close your portal from that side while you close your own portal in this dimension."  He gave a cynical chuckle. "You make it sound so simple.  What do you stand to gain from helping me? I take it there is something you want me to do for your help." I interlocked my fingers, then smiled. "I'll stop the demons coming into the mage guild, so that is enough. But you're right, there is one thing I need you to do. But I'll only tell you in front of the other board members tomorrow. It won't cost you a penny, but the price I will ask my be more then your willing to pay. Now then, I need you to go through my first request: Talking to my students about this mess you've made." I called in Tiberius and the others, each of them surprised that I wanted them to speak with Greg. Greg did what I asked and told them everything he knew, then I did the same. Both Tiberius and Bella took the news hard, discovering that I had known for the last few years where the demons had come from and who was responsible. "Why didn't you tell us sooner?" Tiberius yelled at one point. "I had cursed his name for the past few years, now you're telling me it isn't his fault?" I had enough of being the kind father at that point, so as the strict teacher I said, "Remember when you made your first dangerous level infusion?" That fazed him for a moment as he remembered the disaster that was. "You tried to make it without having enough essentia, then ran around the altar trying to fix it while losing items left and right. Remember how I simply sat in the next room and let it happen, then gave you a hand cleaning up?" Tiberius nodded without saying a word. "That is what has happened with Greg. The infusion has gotten out of control, so now it is time to help him clean it up. I gave you enough time to realize the infusion was out of your control, and you realized it in time and stopped the infusion. I gave Greg time to realize he needs a mage's help to fix this mess, and now he is willing to at lease consider my help. That is why I never told you, to give him a chance to fix it on his own." After I told Greg to leave and get some sleep, I told my students the truth: I had only discovered most of the details in the last few years, and had started looking deeper into it after Bella had met Sinferrius.  The next morning the board members came together after breakfast. Greg fulfilled the request I had yesterday of telling the other members, then came the hard request. "I need you to ask me for help." I said to Greg. He looked puzzled, saying "But I thought you were going to help me already." "No, I can help you, but I will only do it if you swallow your pride and admit you need my help. As I told you, this request might be a price you aren't willing to pay while not costing you a penny." Greg stood up, and with the look of a man who has been utterly defeated said "Magus, will you help me close the portal to Tartarus?" I stood as well, placing a hand on his shoulder and told him "All you had to do was ask." After that we moved onto the topic of the withers I needed to kill. It would be hosted in the nether in one month's time, and all the other members had already decided to cancel whatever they had planned for that day. It is a rare occasion that Hell's Freezer is used, let alone by a few "Representatives of the Mage Guild". They would make the announcements to their lands, and it was to be considered a "Holiday" for everyone so they may watch. Hell's Freezer has a set of stands for an audience to watch it like some sport (which, in all honesty, that is what it has become), with a set reserved for each of the countries that make up the Conglomeration. Most of them have the seats sold to the highest bidders, but a few offer seats to anyone who comes for no charge (like the Mage Guild) or to close friends and family (Raylind Diyo does this, yet he still fills every seat with only friends and family). After the meeting was over, I went to the village and bought a few things from some traders (Tiberius needed more mana beans and wispy essence, while Bella wanted me to look for flowers to add to her garden), and while I was there I traded rumors with the group. I told them that all the Conglomeration members are supposed to be in the Mage Guild's land (They laughed at this, thinking it a joke.) and told them that I heard someone from the Mage Guild was going to use Hell's Freeze in a month's time. In exchange they told me of the local conspiracies on where the demons are coming from (Current believe is that a few miners unleashed them by digging too deep into the earth. I'll let them keep guessing, for Greg's sake.), and word of a war outside the Conglomeration's land (They mentioned a women with black hair and mismatched eyes was leading the conglomeration's defense. Good to hear she's having fun, but I hope she comes home soon.) As quickly as they came, the board members left. I thought my hand was going to fall off after all the handshakes, and I nearly fell over from some pats on the back. It has been a few hours since the portal was closed, and on the trip back I told the others about the deal I had made for the nether stars we needed. Each of them would fight one on one with a Wither to claim its star. Then, after my own brawl with six so I can clear away the rust of time, we would join to fight twenty together. When they asked if it was possible to kill twenty withers at once, I assured them we could do it. I didn't say that the most ever fought in Hell's Freezer was ten withers against three people and two of them died in the fight, as that would worry them a bit too much.  That brings us up to now: I'm preparing myself and my students for the fight, teaching them what I know about killing withers (I have claimed my fair share of nether stars back in my KAMI days) and helping them craft all the items needed for their fights. Tiberius plans to use a combination of his sigils and thaumaturgy to kill them. Vlad is brewing a few potions to give him an edge (I've seen what his best potions do. He can handle it without any problems.), while he is using a few of the sigils he made with Tiberius and some spells from Demir. Demir is going all out with spells, from both blood magic and Ars Magica (I have helped him make a few of my favorites, and Vlad has made him mana potions to keep him going in the fight.) Bella is going to use her demon friends to help while she uses some of the witchcraft I've taught her (she has enough infusion of light power to protect her, and I know Sinferrius would never let Bella take so much as a scratch from some Wither). As for myself, I have been brushing the dust off my old bag of tricks. We will see if they are enough to get me through. If there is another page to this book, you'll know I lived through it. We killed all thirty withers. Vlad, Tiberius, and Demir are injured, but will make a full recovery in a few weeks' time (My regeneration ritual has helped them while their bodies do most of the work.). Bella and I came out with only cuts and bruises, so we have been helping the boys until they have healed. I've had to lock Vlad out of his lab so he will rest, and Tiberius is reading every book I can find for him. Demir meanwhile has ate enough to feed a cow, and yet the boy hasn't gained a pound. Aside from our light wounds, we managed to get all thirty nether stars (the old record was put to shame) and I am waiting for everyone to be fully healed before we put together the altar. The battle to gain said nether stars was hard, but amazingly brief. Each of my students used their respective field of blood magic to kill the withers, and my own Withers took less a minute to kill. Then we faced the twenty together. We used every branch of blood magic to take them down, with Vlad healing and buffing us, Tiberius defending us, Bella using her demons as support, and Demir and I dealing damage. It was a hard battle that lasted less than five minutes, but all of our soul networks were strained by the end of it. Currently, I have just begun researching and experimenting with ways to open a gate to Tartarus using the data Greg has sent me. From simply skimming these notes, I can tell that I will need the help of all my apprentices to build this. I will create rituals as I find time and inspiration, but this gate will most likely fill my days. Until then, I will start scattering copies of this book and the ones my apprentices have made. While I have seen that I have gone off on tangents about things other than rituals, I feel like I should keep it. Reading the books of my other apprentice's, I can see I'm not alone. 
aw.entries.rituals.zephyrRitual=I must admit I have become lax in my updating of this book. Since the last time I have managed to sit down and start documenting, I have been experimenting with the "Reagent system" my colleagues have created as well as forming new rituals. I have not yet finished the portal, but I believe I have come up with a plan on how to build it.... But that is something for later. Starting with the rituals, I have had a few interesting ideas... There was the Call of the Zephyr, a ritual with the purpose of retrieving items off the ground and depositing them into any inventory above the master ritual stone. It does not just call the items through three dimensional space, but use a few new tricks to transport the items quickly and even through solid matter. 
aw.entries.rituals.harvestRitual=Next up was the reap of the harvest moon. What it does it quite simple: It can harvest plants within a 9x9 area centered on the master ritual stone. However, I have needed to configure the ritual for any plants I need it to harvest. A few plants, such as wheat and carrots, I have already adapted the ritual to but I know of several crops that it can't function with this ritual. Later on they might work, but for now if it doesn't work then it might never work.
aw.entries.rituals.eternalSoulRitual=I have tried to improve the feathered knife ritual, but managed to achieve a strange but useful result christened Cry of the Eternal Soul : A way to convert LP from the soul network into liquid LP. It was designed to be constructed underneath a blood altar, and will link to the first one it finds. Afterword, it will drain the soul network of the activator and only if he is in range to fill the altar. This does have a few nuances, however: While inside it's range, the activator is extremely weak to the point of being near death. The conversion rate is rather poor at a two LP spent for each point of liquid added(One is added to the altar, and one used to create it). The altar also seems to consider this the same as a pipe adding LP, so any input is throttled by the buffer. As a result, any altar used will need to be built with usage of the ritual in mind.
aw.entries.rituals.ellipsoidRitual=As a means to aid in construction, I created the ritual "Focus of the ellipsoid". This ritual checks an inventory above the master ritual stone, and after my recent redesign the ritual will check an inventory directly below the master ritual stone to find blocks to construct the sphere(I couldn't get the ritual to function with the inventory any lower. This could be worked around with hoppers, teleposing the inventory, or just having something that doesn't care. I personally went with a simple setup of teleposers, redstone, a few rituals as a cobble stone production, and a hand full of demons. The demons wondered in over night, and I couldn't get them to leave.). It checks the first three slots of the inventory above it, the forms a dome with the dimensions based on the values. First slot is the X-radius, second is y, third is z. This ritual does have a cost per block, so large spheroids will cost far more then small ones. From initial tests, this seems to work well in conjunction with a ritual of the dome for underwater construction.
aw.entries.rituals.evaporationRitual=Speaking of the dome, another ritual was constructed to work with it: Song of evaporation. Basically, this one time use ritual will remove the remnants the dome ritual creates from fluid blocks that allow then to reform. In other words, when the dome ritual is removed the fluids will not return if you use this. Simple, but extremely effective.
aw.entries.rituals.sacrosanctityRitual=As a form of protection, we have created the Ward of Sacrosanctity as a way to deal with the hourds of monsters that form in our world. While I don't feel like going into Dust theory, this ritual blocks mobs from forming inside it's range(Thirty two blocks from the master ritual stone) regardless of light levels. Keep in mind it has a passive cost to run, so make sure you have a soul network strong enough to handle this.
aw.entries.rituals.evilRitual=And on the flip side of this coin, I created the Veil of Evil. It is effectively a way to increase mob spawning(Again, using the principles of Dust theory), and allows them to form regardless of light levels. Once again, it has a passive running cost to a shut off switch is in order.
aw.entries.rituals.stomachRitual=I have had many nights spent researching interrupted by the annoyance known as "Hunger". As ad hawk solution, I have built "Requiem of the satiated stomach". Much extracting food from an inventory above it, anyone inside it's range will fill satisfied. However, it does not actually fill your stomach with food(This turns out to be quite painful), but instead lowers the rate at which you consume food. Effectively, it adds the saturation of the food to anyone in range(So beef would be far more effective then bread).
aw.entries.rituals.reagentEffects.1=I mentioned a reagent system earlier, so I might as well give a brief explanation of it. Using the raw alchemical energy contained inside one of the reagents, they have discovered a way to augment my rituals. The results of this have been interesting, from improving old effects to adding completely new ones. However, these improvements are not free: The costs and if it is active or passive drains depends on the ritual and effect in question. I will try to document the effects, but the costs will be left for the readers to discover(I can't very well tell you everything, now can I? An old man needs to get his entertainment where he can.)
aw.entries.rituals.reagentEffects.2=The full spring has more effects then the average ritual, with Aquasalus letting it hydrate farmland in a four block radius from the master ritual stone. Offensa allows it to function as a weapon, thickening water vapor in the air to the point it is like drowning(By default it leaves the activator immune to this effect), but if it is paired with reductus it will ignore any humans. Crystallos allows it to freeze water, functioning as an ice generator. Finally, sactus allows it to have more control over where it places the water to the point of filling a tank placed over the master ritual stone.
aw.entries.rituals.reagentEffects.3=The serenade has only three effects, with offencia, reductus, and sanctus functioning in the same way as full spring. Main difference is that instead of causing them to drown, it gives them a more... Explosive result.
aw.entries.rituals.reagentEffects.4=The green grove is simpler in it's effects: Terrae and orbis terrae cause crops to grow even faster then normal(Orbis is slightly faster, but the effects do stack). Aquasalus allows it to till and hydrate any farmland above it, but only the three by three so it isn't as effective as a full spring. Virtus extends the range it speeds up growth to a nine by nine above the ritual, but this doesn't allow it to hydrate that far.
aw.entries.rituals.reagentEffects.5=Interdiction has two strange effects. With aether, items become influenced by the ritual as well. Magicalus removes the safety I put on the ritual so that humans are effected.
aw.entries.rituals.reagentEffects.6=Containment becomes more effective at keeping things in with reagent. Crepitous allows it to supress the natural destructive nature of the creeper, while magicales prevents teleportation for mobs inside. Aether has been just a simple extension on range.
aw.entries.rituals.reagentEffects.7=A ritual of unbinding powered with sanctus allows the breaking of a blood pact, so it can be reforged with another person.
aw.entries.rituals.reagentEffects.8=High jump has general effect improvement,  with aether allowing it to fling you higher and terrae increasing the range of fall damage protection.
aw.entries.rituals.reagentEffects.9=Speed ritual has some interesting possibilities when upgraded with reagent, with sanctus and tenebrae allowing it to act as a form of animal sorting. Sanctus limits the effect to baby animals, tenebrae limits it to adults. Aether sends you farther, terrae limits the vertical distance it will launch you. Finally, reductus shields you against any fall damage temporarily for a safe landing.
aw.entries.rituals.reagentEffects.10=Magnetism has purely utilitarian upgrades, with terrae and orbis terrae increasing it's range while potentia improves running speed.
aw.entries.rituals.reagentEffects.11=Crusher has a few more interesting effects, with orbis terrae, potentia, and virtus adding fortune effect. Crystalus adds a silk tough effect(This can be mixed with a full spring for an ice farm), and incendium turns cobblestone into netherrack(Vlad mentioned an idea on autonomous incendium creation with this).
aw.entries.rituals.reagentEffects.12=Shepherd's effects seem to make it better for animal breeding, virtus allowing for animals to... How to put this tactfully... It triggers the urge to reproduce. Reductus on the other hand lowers the natural time between breeding cycles.
aw.entries.rituals.reagentEffects.13=Feathered knife has a few interesting effects to improve utility. Sanctus increases the amount of life force it leaves you with, so you aren't as weak inside it's range. Reductus lowers the range, to allow you to allow you to only be inside the range when only a few blocks from the master ritual stone(Testing shows that it is around the edges of a T5 altar). Magicales limits the draining only to the activator, so your guests don't make an unwilling donation. Potentia has the simple effect of increasing running speed.
aw.entries.rituals.reagentEffects.14=Reductus limits the regeneration ritual to humans, virtus allows it to heal even faster, with praesidum increasing the range.
aw.entries.rituals.reagentEffects.15=The effects on the feathered earth ritual are standard: Aether is vertical range, terrae and orbis terrae is horizontal range.
aw.entries.rituals.reagentEffects.16=Reverance of the condor has only two effects, with aether allowing it's effect to persist a bit longer after leaving it's area of influence and reductus limiting the flight only to the activator.
aw.entries.rituals.reagentEffects.17=Mark of the falling tower has quite a few unique effects, with terrae and orbis terrae buffing the average radius of the meteors(But these effects doing seems to stack with each other), crystalos summons a meteor with ice instead of stone, tennebrea has obsidian instead of stone, and incendium has a variety of nether materials.
aw.entries.rituals.reagentEffects.18=Ballad of alchemy only has a speed buff from potentia, no matter what I try recursive crafting seems impossible...
aw.entries.rituals.reagentEffects.19=Aura of expulsion seems to have two normal buffs, as virtus increases the distance something can be teleported and potentia the range of influence. Tennebrae has the interesting effect of allowing the ritual to teleport entities other then humans.
aw.entries.rituals.reagentEffects.20=Dome of suppression only has two range increases from aether and aquasalus.
aw.entries.rituals.reagentEffects.21=Call of the Zephyr with reductus adds a delay to when it picks up an item/ This has shown some interesting uses in automation...
aw.entries.rituals.reagentEffects.22=Cry of the eternal soul with sanctus leaves you with a bit more life, so you aren't knocking on deaths door.
aw.entries.rituals.reagentEffects.23=Well of Suffering, after a few tweaks on my end, is now able to accept three reagents. Potentia extends the range it has vertically, simple enough to understand. Tennebrea improves the efficiency of the process, allowing you to gain double the normal amount of LP from health then normal. Finnally, Offencia has the ritual damage mobs twice in quick succession, harvesting life essence faster and killing mobs faster.
aw.entries.rituals.reagentEffects.24=Rhythm of the Beating Anvil only has two reagent effect available as it stands. Potentia raises the rate of crafting from five times a second to twenty times a second. Virtus allows it to use the output chest as a fourth input.
aw.entries.rituals.reagentEffects.25=Blood of the New Moon only has one effect, we terra replacing moved blocks with dirt instead of leaving a hole.
aw.entries.rituals.conclaveOfMages=With my experimentation into reagents, I think I have the final piece of the puzzle I have needed to build the portal. I may need to call the conclave of the Mage guild for help executing my plan... The conclave has agreed to my plan, and we have set out on the road to Venric's folly, a somewhat natural prison to contain any demons that our portal spawns so they aren't as dangerous to people. Once we fortify the area, create a base of operations on the walls to view the demons progress from a safe distance, then set up a Nexus link here we will be ready to open the portal. Despite all the hardship it may cause, we will-No, we must open it. To many innocent lives have been lost to stop now.
aw.entries.rituals.forbiddenParadise=It is the eve of Forbidden Paradise, as the other mages have started calling it. The ritual is built and ready, I simply await the final hour before beginning. To pass the time, I will document the theories in place behind... the ritual I have yet to name. We've been so busy the last few weeks I haven't had a chance to give it a proper name! Well then, that will need to be addressed... I feel Bella should christen it. The work is done here, so I shale hold a small ceremony for the naming before sending off the others to a safe distance away.
aw.entries.rituals.convocationRitual=Convocation of the Damned... As fitting a name as any. Well I promised theories, so I'd best get started. The basic construction of the ritual is an improvement on the ritual of binding utilizing dusk runes and requires a far larger footprint to build(Around a hundred and twenty eight ritual stones are needed, I recall). The ritual then needs three investments; Fifteen million LP to start the process, eight full belljars of assorted reagents must be routed into the master ritual stone in any order(The ritual is designed to have them on the pillars, and in fact does not like working without them. The reagents needed are Incendium(Fire), Aquasalus(Water), Terrae(Earth), Aether(Wind), Sanctus(Holy), Tennebrae(Shadow), Magicales(Arcane), and Potentia(Energy)). Since Vlad's reagents have proven successful in summoning demons with Bella's little setup, this applies more power from the raw reagent on a much larger scale from the ritual in addition to the jumpstart LP offers. The final part requires the portal to be "Set" from a sacrifice. Using a blood altar placed above the master ritual stone and a demon, you set the portal by sacrificing said demon with a dagger of sacrifice after all the reagent has been consumed. If all the steps have been completed correctly, then the portal should complete and a link to Tartarius forged. After that... Well, I have theories but only time will tell if they are more then that. It is time to get some sleep, we have a long day head of us tomorrow... It has been a long day, but all of space hasn't collapsed in on itself after our efforts today. Always a good start, at least. The siege team has returned from a successful mission. An interesting side effect of how the portals work cause all demons who have entered via that portal to vanish, returning to their home since the portal acted as their "Anchor" here. But that isn't the big news. What happened after we opened the portal was something none of us expected: The demons started colonizing. They have been building homes, gathering resources, laying down roads... They have been building a proper encampment. The others have a few tales of their own about the demons, as I was out of commission for most of the day(Activating the ritual took far more out of me then I thought. I will need to start organizing our future plans now that this... Invasion has begun.
aw.entries.rituals.longHaul=It has been a little over a week, we've managed to make several improvements to our equipment using some of the shards retreated from the demons, and said demons have shown no sign of leaving any time soon. Since it was my plan to open the portal, I have volunteered to stay here and watch over the invasion and make sure the demons don't get out of check. Already a teleposer link to my homestead has been created for myself and my apprentices to use, and we have started work an a new home here and Venric's folly. But if I must build a new home here, even a temporary one, I should work on getting a few basic amenities to help ease our lives here while we see how this invasion pans out.
aw.entries.rituals.phantomHandsRitual=My first task was to create a ritual to help deal with all these chests we have laying around, as it is getting to be a nightmare finding things after the fledgling thaumaturge Soaryn paid a visit. Having a ritual take instruction, however, was a little harder then expected. After several experiments, I have discovered a relatively simple method to achieve this using chests(Same rules as most inventories with rituals. Doesn't care what it is, from a chest to a furnace. I will be using chests as an example, as that is easiest.) To begin, I need to cover the logistic foci that allow you to give instruction to the demonic force behind the ritual(Only thing smart enough to handle this task). Adjacent to the master ritual stone can be up to four chests, and these hold the "Input focuses" the ritual checks to know what to pull from and how to pull from it. In laymen terms, you right click on the side of whatever you want the ritual to pull out of and it will follow all the normal rules for extraction for whatever side you clicked on.  The output foci need to be placed in chests located at the four nooks in the V's of ritual stones and roughly two blocks away from the master ritual stone. They follow the same rules for marking, right clicking on the side you want the ritual to send items to and following item input rules for the block. However, there is one addition: If you shift click on an inventory, it will not only mark it as a destination but save the item total of the inventory. If the focus has a saved total, it will keep the bound location stacked with that amount for each of it's set items. Note: You don't need the final destination to hold all the items for total binding, you can use a second inventory as a middle man to set the total and then bind the focus to the new inventory without changing the total(Make sure you don't shift right click on the new inventory with the focus as that would reset the amount). Should you wish to remove the item limit, shift right click on an inventory without anything inside.  There are a few variations of the output focus, each with subtle differences that impact operation. The red "Default" focus is the standard focus, has no special properties to it. It will ignore the NBT of items, and will check the "Metadata".  The blue "ModID" focus instead of checking exact items will instead check the origin and classification of the item and will send anything that matches this. For "Origins and Classifications", I mean anything made my the Industrial craft corporation will be sent to the same location but anything made by their splinter corporations like nuclear control or advanced solar panels will be sent to different locations. The rules behind what belongs to what are archaic at best, but the scribes and scholars at NEI and WAILA keep exact records of these things so seeking their help may be needed if you don't want to rely on trial and error.  The dark green "Metadata" focus isn't as exact with metadata, allowing items such as dye and colored glass to have all the subtypes and variations sent to the same location without needed to specify every single one. This also applies to damaged armour, weapons, and tools. The Yellow "NBT" focus is very exact with what it sends, keeping track of the tiny hidden differences such as how much Vis a wand has, what setting a ritual diviner is on, where a spell crystal is bound, ext... It also checks meta data, so keep that in mind. Finally, the purple "Global" focus needs you to set any item to send(What it is doesn't matter), and afterword when the ritual reaches this task it will send anything it possibly can. This is useful as a last resort location for anything you don't want to have sit in a source location but you have not set anything for the ritual to do with it and don't want it to clog up the system. Now then, the ritual itself. As I stated, it will check the adjacent for where to pull items out, what are the source locations. The output foci set where to send any items from the sources, what is the limit on what to send, and all the rules on what it should be sending. To specify this, you need to place one or more output foci in one of the chests to tell it where to send it and how to look at the items, then one or more items after it to specify what it is to be looking for. If the ritual finds another routing focus after the last item in a "Command chain", it will create a now chain following all the same creation rules as the first and acting independently of it. I should note that the ritual will work sequentially, sending all possible items to the first focus location it can while still following all the set rules, then move on to the next focus and doing the same. Additional quirks of using more then one output foci in the same chain: If more then one of the foci have a item limit set, the limits will add together so having four foci with sixteen set as the limit as sixty four. Also, the ritual has some logic for sending items in a chain: For all foci types bar one it will act as "OR", meaning it can send items if any of them match and will go to the first match. The NBT focus, however, acts as an "AND" and the ritual will only send items if they match both the current focus and the NBT focus rules.
aw.entries.rituals.anvilRitual=With a way to move items around at hand, it is the next logical step to have a way to refine items automatically without needing to spend hours at a workbench crafting. Cue the creation of "Rhythm of the Beating Anvil", and I must say I'm rather proud of how it turned out. The ritual will look two blocks above the master ritual stone for up to nine inventories, and will use the items it finds in each inventory to try and form a recipe, then pull out of the three input inventories one block down and two out from the master ritual stone and next to the earth ritual stone to form the item and finally push the crafted item into the output chest(It's side is marked by a water ritual stone next to where the inventory goes). For shaped recipes, it will use the output location to determine where is the "Bottom" of the crafting grid is located, and will form recipes using that as a reference point. Now then, how you set a recipe is simple: In the first slot of each used inventory, place any item the recipe needs or leave it empty if the recipe doesn't need anything there(Like the middle for a furnace or chest). Place the used items in the inputs locations, and the ritual will craft up to a stack of the output before stopping. The ritual will then check the second inventory slot of each inventory for a recipe, try to craft it, then repeat for the third, then fourth, then fifth... You get the idea. The more inventory slots you have, the more recipes one ritual can handle.  A tip for use: Normally chests don't like having more then one side by side, due to the parts used to merge to chests getting in the way. However, this does not apply if you alternate normal chests and trapped chests.  While it will be added post script to the reagent effects, I should mention the effect of Virtus to this ritual as it is rather useful: It will allow the ritual to use the output chest as another input chest. While that doesn't sound useful, it actually allows for reciprocating crafting(An example of this is imputing logs, the ritual turning the logs into planks, planks into sticks, planks and sticks into signs, planks into chests, ect...).
aw.entries.rituals.dawnInk=This "Demon invasion" as we've come to call it has born some interesting fruit, one of which was a gift from Tiberius in the form of a new ink for my rituals. He has requested that I aid him in construction of a sort of "Chamber" and a ritual to power it, but is keeping some of the important details from me for what he says will be a "Big reveal" and would "Spoil the fun".
aw.entries.rituals.symmetryRitual=After a few weeks of design and testing, we have finished the ritual Tiberius wanted in order to achieve what he and the others have dubbed "The Omega state". I'll cover the ritual side of the process, and allow the others to go over the details of it's functions. For this ritual to function, it needs a sealed space in order to fill with reagent like a gas cloud. This reagent is then absorbed by the armour as the ritual merges the souls of the activator and the demon inside the activators bound armour. The problem is containing and controlling this gaseous reagent, as it likes to leak out of the chamber and the ritual can have difficulty "Pushing" the reagent into the armour. To address this, we created Glyphs. But I'm getting ahead of myself. First, I should cover how the ritual determines what is a "Sealed" chamber as it has safe guards to prevent reagent loss. The reagent gas is let lose above the master ritual stone, so the ritual starts checking there. It then starts expanding from there, searching for ways out in every direction, and if it can't get five blocks away from the master ritual stone(This isn't random, beyond five blocks away in any direction and the ritual has difficulty moving all the reagent. Think of it like compressing a gas, it always tries to move down the path of least resistance. To compress it, you need prevent it from being able to escape.) I have also noticed that the reagent, after being released into the chamber, collects on the walls of the chamber instead of staying in the air like tiny low hanging fog. The more surface area, the thinner the fog becomes and the more stable it is, and more stable fog means better value the ritual gets on the conversion process between reagent in the jars and the reagent in the armour, so the more time you have in the Omega state. No matter how you cut it, this chamber is meant to contain reagent for long stretches of time. The more stable the reagent fog is, the more time the ritual has to work and the less reagent wasted. Now then, I mentioned Glyphs earlier. After seeing the chamber in action, Tiberius created a few blocks called "Glyphs" to optimise and add to the champers use. First, the Glyph of Rigid stability was created to further stabilise the reagent fog, being worth roughly double the amount per square meter of space then a normal block.  But that's not all he made with the Glyphs, as he discovered an interesting way to bolster the power of the demon using two new Glyphs for a system that resulted in something like enchanting. He mentioned two values, and had created Glyphs for each of them. First was "Enchantability", and it's is buffed by Glyph of the Adept Enchanter. The second value was "Arcane power" with Glyph of Arcane Potential, and functioned like the levels used in normal enchanting. To put how the two values work in layman's terms, Arcane power effects what enchantments are possible to receive, while Enchantability effects the odds of getting a good result. Since this process doesn't use normal enchanting(As the demons in our bound armour react negatively to such methods normally. This process circumvents this limitation by being more demonic in nature and this more... Tolerable to the demons.), it doesn't follow all the normal enchanting rules. For example, after much testing we were able to achieve the equivalent of protection six, fire protection four, and blast protection seven on a single piece of armour so some independent enchants can coexist. And normal power restriction also don't hold ground here, as seen by getting blast protection seven... More testing must be done, but you can see how this has potential.  However, I should mention that a chamber can't be entirely enchantment blocks, as the Reagent reacts negatively to the magic used in them. While the reagent must touch the blocks to activate them, any fog that forms on them will destabilise severely causing less time in Omega. I have put in a safety to make sure it can not trigger the process if the stability is below zero, as that would be closer to a bomb then a fog at that point. I believe all the information you need to know before starting has been covered, so we will cover the actual ritual activation. The ritual is a flat platform with four holes, and in these holes you place the belljars of reagent to be used. This ritual doesn't use reagent in the normal fashion, so you don't need to route them into the master ritual stone like normal. With your awakened crystal, activate the ritual with your awakened crystal like normal. After the fifteen million LP fee is paid, it will look for belljars in the four holes for the thirty two thousand reagent cost. Once both are ready, it will check for the activator wearing bound armour and if present it will kick start the process. Within seconds, the transformation happens and you will enter the Omega state.
aw.entries.rituals.stallingRitual=After watching the others toy around with Omega, I realised how limiting the fact that Omega burns reagent to maintain the form can be. With this in mind, I have created a ritual to strengthen the bond between the two souls and remove the passive reagent cost of maintaining Omega. Named "Duet of the Fused Souls", it is able to replace the reagent cost with a high LP cost(Tiberius says it needs five thousand a second to run, far more then most setups can handle. At best, this ritual holds off the inevitable unless you have an extreme generation rate, and even then you will will have a crippled net gain.). You will still lose reagent to replenished your demonic resistance, so long term combat even in range of the ritual will eventually burn through your reagent supply. I should mention that this ritual doesn't have enough power on it's own it work, you need to build it around a tier four beacon. Once it is in place, the ritual can use it as a power source and will allow it to have an active range of roughly double that of the beacon.
aw.entries.rituals.newMoonRitual=Feeling unsatisfied with Focus of the Ellipsoid, I recently restructured the ritual to slightly change it's effect. In doing so, I found inspiration and created my new work "Blood of the new moon". In essence, it moves a small planetoid of blocks from an assigned distance below it to a mirrored distance above it. Magic behind it is slightly more complicated then that, but you aren't here for spell theory 101. To operate the ritual it is fairly simple: Above the master ritual stone needs to be an inventory to give the ritual instructions. The first slot sets the radius of the sphere, but is limited to a maximum thirty two. The second slot defines how far below it looks for blocks and above the sphere is created, with the distance defined being where the centre of the sphere is. As a bit of a safely precaution, the distance must be at least three blocks greater then the radius to prevent harm to the ritual. I should note that if there is an obstruction in the destination, instead of the two blocks being swapped the ritual will "Skip" that area. A final foot note: If this ritual is fed terra reagent, instead of leaving a hole where it pulls blocks it will instead create a block of dirt. A nice option to prevent clean up of a huge hole under a new sky base.

aw.entries.demons.ashes=My name is Bella Highborn, and I am a blood mage. One month ago, the village I called home was attacked by demons in the night. I can't remember much of the attack, but I recall so many screams of pain echoing inside my head. It took me a few minutes after covering my ears to realize that the screams I kept hearing were not coming from the people. Shortly after the attack started, I ran from my burning home to try and hide in a small alley, behind a large waste basket the butcher dumped his scraps in. At one point I thought I was dead as a demon with long limbs, a gaping maw, and huge teeth lumbered into the ally. It looked strait at me, it's nose flaring as it did so, its blurry eyes looking down at me. It then piked up the basket I was hiding behind, ate everything inside, threw it over his shoulder onto a burning building, and then, left me without a scratch. Several other demons found me that night, and all of them had the same blurry eyes and all of them left me alone. At some point I managed to cry myself  to sleep. The next day I awoke in the same little corner, the smell of death strong in the air. I wandered among the remains of the village, finding the burnt and frost covered bodies of my former friends everywhere, all while asking myself why I was spared when they weren't. After several hours, I finally heard a sound other than the crackling of embers from all the old buildings: Voices. Human voices. I looked around to see a small group of men wandering through the ashes. A few of them were bickering about something, occasionally pointing in my direction. At the distance I was at, I could barely make out the words "Girl", "Demons", and "Caravan". I had started to wander closer to them, a little afraid to get too close. Suddenly, one of them broke off from the group and took a few steps in my direction. After a few more quick words to the others, he turned to start walking towards me.As he got closer he started asking me simple questions. like if I was injured and if I could walk. I was still to upset to give him more of an answer than nodding or shaking my head. And then he said full of regret : "I can't do anything for the dead and I can't give you back your old home, but I can try to give you a new one. Care to take a risk and come with me?"his voice softened and he looked at me with his light green eyes. I thought of everything that had happened in the last few hours. Everything I ever knew was ripped away from me. I had no home. No family. No distant relatives. No chance to keep living here. I gave him a timid nod, then took the hand he offered me. I soon found myself walking down the only road that lead away from my village with the green eyed man, who told me his name was Vlad, and his brother Demir. We walked till my feet started hurting and then some, till dusk, when they set up camp. They asked for my name, and what happened the night before. I shook my head as memories of all the screaming came back. They then asked if I knew any magic, to which I shook my head again. I knew very little of magic, despite living so close to the mage guild's boarders. I then turned to look at my former home, now a few miles behind us. I silently said my last goodbye to the life I once knew, then fell asleep thinking of what may lie ahead of me. My dreams were filled with nightmares with scenes from the last night, and a hundred voices crying out for help. But all that still walked through the village were the demons.   We walked for the next three weeks, with Vlad and Demir selling what little they had on them to fund our trip. Several times they mentioned a man I had never heard of : "Magus Arcana". Vlad spoke highly of him, while Demir didn't seem as impressed. We traveled almost all the time we where awake, and when my feet hurt so bad that I couldn't walk anymore either Vlad or Demir would take me piggyback. One evening I asked them why they are doing so much for me, both put on a weak smile and said "We know what it's like to lose both your home and your family. We lived in the streets for a few months before the Merry Lamb took us in. We didn't want you to go through what we did." For the first week I had that same nightmare every night.  The second week it was every other night. By the time we reach a village named Lurric, they had almost stopped. I went with Demir to buy some supplies, and when we regrouped with Vlad he had a grin on his face. "We're almost there. You were right Demir, this is the town where he lives." After a few minutes of walking, we stood at a stone brick home. We had only one of those in my old home, and that belonged to barracks of the guardsmen. As we walked up to the door, I found myself hiding behind the brothers. I had heard some of the legends about the man who lived here on our trip, and anyone in there right mind would be nervous knocking on his door. After a brief chat Vlad had with two men, we were let inside. I thought I should be scared, but both of them seemed so nice that it was hard not to feel at home here. But as I stepped inside, I started  hearing whispers. At first they were only a few, but as I sat down the whispers grew a little louder and more frequent. Soon after that I was distracted by my belly reminding me I haven't eaten since yesterday morning, and the fact that there were sandwiches on the table for us. As I ate, I tried to listen for the whispers, but they were still so faint that I couldn't make out what was said. Giving that up, I started paying attention to what the others where talking about . Unfortunately, I was a little late. Magus had already started getting up, while Tiberius was taking away the trays that once held sandwiches. After a few minutes, both Magus and Tiberius left to take care of something in the village leaving us to settle down a bit. "They're an odd bunch." Demir said soon after they left, starting to look around the room we were in. "They can say the same about us." Vlad said, and I couldn't help but smile at his deadpan humor. After an hour of milling about the place, gawking at all there was to see, Magus and Tiberius returned. We ate a large dinner Magus brought, then they showed us where we would spend the night. Magus lead me to his room, telling me that I could sleep here for the night while he would take the couch we had sat on earlier. Once we got inside, he closed the door as I took a seat on the surprisingly hard bed. "Now then, I have heard a few rumors about you Bella." He sat down next to me on the bed before continuing: "Mind if I ask what happened to you?" I hadn't told Vlad and Demir yet, but I finally had the strength to speak about what happened. After I finished the story of the attack, I told him of my nightmares and hearing countless voices crying out for help but there only being demons around me. Magus simply nodded, then said "I believe you. Your tale seems a little far fetched, but I can tell you speak the truth. I advise you to keep this story to yourself, but telling Tiberius or the other two should be fine. I'll need to think about this..." He said as he moved to get up. "Wait," I called out before he left. "Don't I get to ask you a question now?" He seemed a little puzzled, but said "Go ahead, ask away." "Do you know what is causing these whispers I'm hearing? I started hearing them as I came inside your home, and they seem to be coming from below us." He told me he heard no such whispers, but offered to help me find the source. After showing me the door that lead to a staircase, we went down two floors till I could more clearly hear the voices. It seemed like idle chatter, and the voices lead me to a box inside a store room that held several bright red blobs. As I opened the box Magus asked "Are these the source of the voices your hearing?" I nodded before asking "Should I try talking to them?" Magus pondered this for a moment, before saying "No, not yet. You still need to get settled in, and there are countless other preparations to be done. We can return to this later, when we have more time. For now, let's see what Tiberius is doing with the others." He lead me back out to the stairway, and I noticed him locking the door to that floor before he joined me up stairs.
aw.entries.demons.tamedDemon=A few days later, I was learning Botania magic from Magus. He showed me the garden he kept near his home, then we went through the names of all the flowers there. Just as we were getting to the more magical flowers and their uses, I started hearing a voice from my nightmares. There was only one voice saying the same two words, and it was slowly growing louder. "Bella, what's wrong? You're turning pale!" Magus said when he noticed me freezing. I stood up, and looked towards the origin of the voice. Then I saw it in the distance. A demon. Just one, wondering seemingly aimlessly. Magus saw it as well, he told me to stay still while he dealt with it. But I couldn't do that, I couldn't let him hurt something so lost and confused. Like it was. Like I was a few short weeks ago. I started running towards the demon, and saw it was one of the more human looking ones, with small wings on its back. It turned to me as I drew closer, and I saw it had the same blurry eyes all the other demons had.  It didn't try to attack me, or really even move when it saw me. All I could hear was "Help me", over and over again. "Bella, back away from that!" Magus yelled as he followed, but I could barely hear him over the voice ringing inside my head. Then something compelled me to reach out and touch the demon, some inner instinct that had been dormant for all my life. Magus was close to us as I touched its hand, and I felt this surge of power race from me into the demon. Suddenly, it's eyes lost the blurriness and he seemed to be "Awake" now. And very confused. Suddenly, Magus was at my side and was trying to pull me away from the demon. Meanwhile, the demon was trying to work out what was going on. "Calm down, both of you!" I yelled, and Magus and the demon did just that as they started staring at me. "Magus, notice how the demon isn't trying to kill us right now. I don't think he was in control of his own body til just a few seconds ago." The demon nodded, before attempting to talk in a way Magus could under stand. "I... was trapped inside my head. I could see everything that happened, yet I had no power over my actions." Magus was still very wary of the demon, but asked how he got here. "I am not here by choice. I remember finding a strange metal thing in my home, being pulled violently towards the metal thing, then waking up in this strange place. I soon realized that I had no control of my actions, and have been wandering ever since. That is, until my brethren here freed me." "Brethren? I am afraid I am human, not demon." I said, confused by this claim. "Really? Your aura looks demon to me. I could tell that even before you freed me. As for him..." The demon turned to Magus. "Yours is like nothing I have ever seen before. It is a strange color, and is far larger than that of any humans I have seen." "Really, you can tell all of that just by our auras? Fascinating..." Magus said, the last of his wariness was trumped by his scholarly nature.  "Well, I have always been better at reading them then many of my kin. But that can be saved for another time. Humans, I must ask you for your help. I can not stay here, your kind will hunt me every day I live for the crimes my kin and I had no say in. I must ask you to kill me." "Why? ", I asked:"Can't you try to hide or.." Magus put one hand on my shoulder. "No, he may be killed, but won't die. Supposedly, if a demon is killed in our world they return to their home in Tartarus. I assume that is why you wish us to kill you?" "Yes, that is true. But I can't ask you to do this for nothing... I sense something nearby that may be of assistance. Will you take me to your home?" He asked. Magus was more curious than cautious at that point, and lead the demon(Who we learned went by the name of Sinferrous) and me back. As we all walked inside Magus said "Vlad, Demir, Tiberius! We have a guest here." All three came out of the work room, and all three paled as they saw Sinferrous. "Greetings." he said as the others  arrived.  "Magus.... why is there a demon in our home?" Tiberius said, growing pale. Magus quickly told them what had happened and what we learned. "Now then Sinferrous, can you lead us to the thing you sensed?" After leading us down stairs, Sinferrous went strait for a strange red orb that was near my brothers work station. "This will do. Using this, I can make a pact between us. For your help freeing me, I will make the pact now. But my kin may not always be as willing, and you may need to defeat them before they will form a pact. Now then Bella, give me this orb." He handed me the orb, then I handed it back to him. As soon as it touched him, the orb vanished into a red mist. "The pact is done, and I am now at your command Mistress. Now, I request that you send me home." "Wait." I asked him. "If we send you to Tartarus how will we ever see you again?" "Simple. When my kind are killed in this world, we can leave behind a "Summoning Crystal". If you use this crystal, we will return to aid you. Because we made this pact, anytime I die in this world I will leave behind a crystal so you may summon me again." We all went outside to send Sinferrous home. Magus had volunteered to do the dirty work, and our new demon friend stood ready. "Before I do this, I have one final question." Magus asked. "You said there was some sort of metal contraption that forced you here. Did you see any symbols anywhere on it that might help us work out who created it?" Sinferrous nodded. "There was one symbol that seemed dominant... Here, let me see if I can copy it..." He began to draw in the dirt, and we saw something, that we never expected: The letters "GT" inside the picture of a gear. "Greg tech..." Magus mumbled, narrowing his eyes. Magus sent Sinferrous home, and it seems Tiberius needed some time to think. "His goal in life, getting revenge against the demons who destroyed his home, was just torn apart." Magus told me that evening. "Give him time to piece together a new goal. He'll recover, I can guaranty you that."  The next day Magus and Tiberius told us about blood magic, where the orb I used just yesterday came from, how it was made, and all the risks of trying to make something like it required. They then asked us all a simple question: Would we like to be blood mages? If I said yes, Magus and Tiberius would help me with taming demons. If I said no, they would still teach me other forms of magic. There was no choice, I needed to discover more about this strange power I had. If that meant I had to become a blood mage, then I would become a blood mage. After Vlad, Demir, and myself passed a "First Test" of creating a weak blood orb, the same type of orb used to form a pack with Sinferrous, the three of us were blood mages. Tiberius gave all of us books like his own, and asked us to document our finding in it. So that brings us up to today, I will try to write down anything that happens with either my work as a blood mage or my life with the others. For now, I must get some sleep...
aw.entries.demons.future=My last entry was a week ago, and since then I have learned a much from Sinferrous. Apparently they have three main groupings of inhabitants inside Tartarius: Lesser demons that act as workers, guards, and servants for the second group called the greater demons. This was similar to peasants and nobles here, but the third group was something all together new: Elementals. Having so much demonic energy in one area causes strange things to pop up, entities of pure demonic power mixed with one of six elements. To the demons who live there they are little more then wildlife, but to us they are extremely hostile and dangerous beings. From what I could gather, each Elemental had several abilities that were common among them while also having a power unique to their element. All of them could fly, cast buffs on themselves to reflect damage, and they cast a strange effect that nullifies some powers of souls. He couldn't tell me much about the unique effects, but warned me getting in a fight with one unprepared would mean death. I noticed Tiberius growing less and less wary of Sinferrous as time went on, but I feel like he wont ever forget what happened to his home regardless of if they had a choice or not.  My last entry was a month ago, and this is more of a progress update then an entry. Magus, Tiberius, Sinferrous, and myself have started building a way to form a temporary "Gate" to Tartarios to challenge demons, but we've ran into a few complications. Magus and Tiberius have already started work on upgrading the altar again, so we might be able to start summoning demons soon. In other news, the training Magus has everyone go through keeps getting harder and harder every week. But considering I would have ruled off what we do daily now as to much for me a month ago, we've made progress. It's been a little over two years since I last wrote in this. Finding what material the altar needs as an amplifier takes much longer then I had anticipated. Thankfully, we discovered a recently that the weak blood shards Magus had collected years ago mixed with stone to create a large amount of blood red blocks we have dubbed "Blood stone". The altar has responded well to the stone, so we have created pillars around the altar and are now searching for the item needed to create a blood orb of a higher quality. I've been binding my time helping the local area with demon problems, and Magus even lets me go out on my own to track them. I've formed pacts with almost a dozen now, and my brothers have always been glad to see what new friends I make on my trips... It seems so natural to call Vlad and Demir my brothers now, but I can't recall when I started referring to them as that. After everything we have been through, it feels like not calling them that is a little insulting. We have become so close in our time with Magus and Tiberius that "Family" is the only word I know for it. A little over two years ago they were nothing but strangers who saved a little girl from a village already burnt to the ground, and now look at where we are. Life is strange, that's for sure.... In other news, Magus has started teaching me witchery, as the botanie had grown to limited.
aw.entries.demons.knight=It has been another year and a few months, but we finally found the item needed for the blood orb: The very weak blood shards we had been using for everything else these days. At no point had it crossed our minds that it might be used for this as well, but we found a few new items in the search so not all is lost. One we made the first "Master" blood orb, Magus invited all of us to the finest meal house in Lurric as a treat. We all dressed in the common cloths we keep for when we wanted to blend in, and we went done to a small family ran business that had some of the best food for miles. We all ate our fill, which for Demir is easier said then done, and as we walked home we passed a man in unadorned armor who was sitting in the street like he had been thrown out of somewhere. He looked at us when we walked by, but he quickly tried to stand up when he saw me. "YOU!!.." he bellowed in a slightly slurred voice that made we think my guess about him being thrown out wasn't far off the mark. "You're that girl I've heard so much about... That one from the demon thing... ATTACK, you're from that attack! I've come here to claim the life of the demon girl as retribution for what happened to that poor village!" He started preaching, before realizing we had already started walking away. "Don't you run from me... You can never outrun the justice that will deliver my blade!" He cried as he charged at us, drawing his sword as he did so. Unfortunately for him, his assault was easily dodged by the simple act of stepping to the left. He ran past us, then tripped on a rock and fell to the ground. As he struggled to stand, he said to the others "Don't you dare try to help her! I will slay you as well if you attempt to do that!" Magus smiled and said to him "We wouldn't dream of it, sir. After all, she doesn't need our help to deal with a senseless fool like you." Magus dropped the smile and turned to me. "Alright, now is as good a time as any to put your training to work. I give you permission to deal with him as you see fit." By now the man had stood, and was starting another charge at me. Instead of stepping to the side like last time, I ducked under his sword, then grabbed him by one of the exposed straps on his armor. Once I had a good grip, I turned to the small crowd of people watching. "Any one here mind if I toss him in the slosh pit?" The slosh pit was the name of the depression behind a few of the building where water gathers when it rains, and for a few days after word it is nothing but a muddy pit. Nobody objected, and a few even offered to give me a hand chucking him. I told them I could handle it myself, then proceeded to drag the fool around a few of the buildings, all while he demanded to know what was going on. The crowd cheered when I finally got him to the edge of the pit, grabbed one of his arms, then flung him face first into the slosh pit. We all returned to our home without any mud on our clothing, but the same can't be said for his armor. I'll note if I ever see that man again. It's been two weeks, so I'd say he left town after waking up in a mud pit.
aw.entries.demons.demonShard=With the help of Sinferrous and my other demon friends I have finished building the summoning area. The smaller pillars need to be in just the right points for complete balance, so it is tricky to build the ritual area. But after a few failed attempts and some help from Vlad, we have the ritual area we needed.  Today, by accident, we discovered how to summon elementals. I'll write more about them once I know more, but they are as hard to kill as Sinferrous warned. We also found after killing it they can drop a strange blue blood shard we have dubbed a "Demon" blood shard. It's been a week, and each of us has earned a demon blood shard. Magus has used his to empower his activation crystal, and the rest of us are saving them to create blood orbs(I still wonder how Magus gets by without any blood orb).
aw.entries.demons.demonSummoning=Something odd happened today: Magus asked me to show him how to tame a demon using my summoning area. He asked me to go over everything, from the basics of how it works to the taming itself. I write here what I can remember telling him. Alright, in the center we have a arcane plinth to help form then anchor a "Gateway" to Tartarius. The plinth needs a blood orb on it to act as a "Beacon" for the demons, but only a master blood orb has the strength to pierce the barriers between worlds. But the plinth alone isn't strong enough to keep the gateway stable, and we can't make a "Universal" portal. We have pedestals around the plinth to help keep the portal stable by sacrificing items, but this has the side effect of "Locking" the portal to one type of demon. So if you use the same "Recipe" five times, you get the same type of demon five times. Some of the demons need more then the six at the bottom to keep it stable long enough to bring one in, so there can be a second or third ring if need be. Once the portal has formed after the last item is placed on the pillars, items will be consumed one by one to keep the portal stable while the demon is pulled in.  Once the last item has been consumed, the demon will be summoned on top of the plinth. The demon will be confused, and will see you bringing it to the overworld as some form of a challenge. Then it will try and kill you. If you beat the demon, it will drop a crystal to summon it back to the overworld(And it won't try to kill you this time). By giving it a weak blood orb you form a "Pack", to make the demon follow any commands you give it. Unfortunately the most powerful things we can summon, the elementals, aren't willing to serve anything due to their inherent chaotic nature of how they are formed.
aw.entries.demons.keysGate.1=Here I will add any recipes we discover that can summon a demon, but at the request of Sinferrous, I will not describe the demons in any way. It will be a mystery till it is too late to go back. Note: Some of these recipes might need more then the first ring of pedestals, so if that is the case the first set of items will go in the first ring, the second in the second.... You get the idea. Remember that the arcane plinth needs at least a master blood orb to work as a beacon.
aw.entries.demons.keysGate.2=Fallen Angel: Santus | Sanctus | Sanctus | Aether | Terrae | Tennebrae
aw.entries.demons.keysGate.3=Lower Guardian: Cobblestone | Cobblestone | Terrae | Tennebrae | Iron ingot | Gold nugget
aw.entries.demons.keysGate.4=Small earth golem: Clay | Terrae | Terrae
aw.entries.demons.keysGate.5=Ice demon: Crystallos | Crystallos | Crystallos | Aquasalus | Sanctus | Terrae
aw.entries.demons.keysGate.6=Boulder fist: Terrae | Sanctus | Tennebrae | Bone | Steak | Steak
aw.entries.demons.keysGate.7=Shade: Tennebrae | Tennebrae | Tennebrae | Aether | Glass bottle | Glass block
aw.entries.demons.keysGate.8=Bile demon: (Poisonous Potato | Tennebrae | Terrae | Raw porkchop | Raw beef | Egg) - (Crepitous | Crepitous | Terrae | Iron block | Iron block | diamond)
aw.entries.demons.keysGate.9=Winged fire demon: (Aether | Incendium | Incendium | Incendium | Tennebrae | Netherrack) - (Diamond | Gold block | Magicales | Magicales | Firecharge | Coal block) 
aw.entries.demons.keysGate.10=We later found that placing a full ring(6) of certain alchemical elements(Earth, Air, Fire, Water, Light, Shadow) will summon an elemental of the respective type. And they are the only way to get demon blood shards.
aw.entries.demons.futurePlans=That is what we have so far. We are working on trying out a stronger blood orb, but Magus keeps telling us that what we need to upgrade the altar is extremely hard to get. To bind my time I have started talking with Sinferrous about "Greater Demons". We have decided that our next goal is to start summoning them, but there auras are so strong that the gate way our master blood orb can form is far to weak. We will need something better, and so we have reach square one again of needed the stronger orb. Well, the Hell's harvest festival is soon so I will write if anything comes up. Strangely, Magus mentioned having replies from people he is inviting coming soon. Well I had no idea what was coming with the Hell's harvest. Magus calls up the doorway from the ground to a giant building full of technology, something extremely rare in the mage guild. Most people come here to get away from all the technology of the other lands, to get back to basics and make there own way in life. Yet what I saw down there was beyond even my wildest dreams. Tech that was only spoken of in hush tones, machines that aren't even available to privet businesses, let alone the public! Magus had mentioned once before that he worked on the conglomeration's members board, but this... I thought he meant that he was a grunt worker, or some administer or mangers. But he is on THE board, where all the worlds leaders meet... And Magus told us they will be meeting here this year since he is the host. I always thought Magus had secrets, but reality seems to go beyond my wildest dreams. Magus asked us to help prepare "The Hall" as he referred to it as. After a few months of hard work, the Hell's harvest is only a few days away. Magus started calling in favors for help, and the boys started talking about just who would be coming. Only now have I realized that I'm going to meet with some of the most powerful people in the world! Magus gave us a few books on every member, so we would know something about each of them. Sadly, the books he gave us didn't include pictures so we had no idea what they would look like. I'll write again after the festival is over.I have meet so many people the last few days, from so many places and from countless walks of life. I became friends with a woman by the name of Iza Lemming, and another girl about my age by the name of Carrin Calclavia. For such amazing people, they seem so... normal. Another man I met Magus keep referring to as "Greg". As it turns out, Greg owns Gregtech, and he is the reason the demons are running ramped like they are. Strangely, I don't hate him. By all rights I should hate him for everything he has done, all the people who have died to the demons... But I don't. From what both Magus and Greg have told me, he did the same thing I did: Opened up a portal to Tartarius without knowing what would come out. The real difference was that I had a demon to help me build the portal, and he had to do it from scratch. Without the help of a demon, the portal he opened was unstable and drove any demon to walk through it mad, and they lost control of themselves.  He had no idea that would happen, that that could happen. I can't bring myself to blame him for something he could have never predicted. In other news, Magus has gotten us the nether stars we need for our next altar upgrade. The bad news is we must kill the withers to claim them. Thirty withers, to be exact. I have mastered witchcraft, botanie, and have learned what I can of blood magic from my brothers and Magus. But will it be enough to kill one of those... things? I asked Sinferrous this one night, and he told me that he no idea what a "Wither" was, but if I needed to fight one that he was willing to stand by my side, as would any of the other demons I had a pact with. With all of them by my side, I might stand a chance. We beat the withers. All of them. I... was terrified of it from the moment I saw it. I had three skulls for heads, and it made unnatural sounds as it started warming up. Then the attacks it used, launching flying blue and black skulls, were devastating in the blast and the "Withering" effect they leave behind if you get hit. Thankfully, the "Freezer" we fought them in was completely blast proof(Warded octuple compressed cobblestone) so falling into holes was not a problem. After an eternity compressed into an hour, we had beat all thirty withers. My brothers are all injured, so I need to go change a few bandages. I'll write again when I get a chance.  Now that my brothers have fully healed, life almost seems to have gone back to normal. Well, as normal as it will ever be in a house full of mages. I've started experimenting with nether stars in my efforts to summon greater demons, but I still haven't managed one. In other news, the number of demon attacks has been getting smaller as of late. Magus said this might be the calm before the storm, and asked me to start leaving the mage guild to help with demon attacks in the other lands. Most people are happy to have my help, but others... Lets say I rarely sleep in the inns and leave it at that.
aw.entries.demons.demonInvasion=Magus has sent out a call for all free mages, with little in the way of context for why. Even I don't know what this is about... He did say there would be a in a little over a week a picnic, however. But knowing him, he has something up his sleeve. The conclave of mages was held today, with some... Interesting results. Fifty people in total showed up, from all ways of magic and life. We met on a hill under the shade of a grove of trees about a mile from here, many of the mages brought there own meals for the picnic. After ten minutes faffing about with people getting settled, Magus called the conclave to order. "I'm sure you all have ideas on why I called you here, and I know Jarico was running lots... So I might as well sate your curiosity. I finally have a plan on how we can deal with the incursion of demons. For the last few years they have been a hazard, an endless army we have fought off. The two things that have complicated our efforts: The fact we didn't know where they have been coming from, and the side effects of closing the portal. Thankfully, I have managed to address both issues." At this point Magus unrolled a map with a few hand drawn markings, which everyone crowded around. "This circled area here is the location of the portal they are getting in, A secret gregtech research lab in the middle of a forest. I have gained permission to mount an excursion to close this portal, but it sadly isn't that simple. The portal is unstable, and has been slowly damaging the fabric of space here."At this point one of the others interrupted with "But if it is slow, how bad could it be?" Magus responded with "Very. This portal has been left open for the better part of the last three decades, and the damage keeps getting worse and worse. As it stands, closing the portal might cause more harm then good as strange as that may seem." A different mage, one a bit older , a little wiser, and a lot more cranky then the first interrupted this time. "If that is the case, why have you called us here? I assume you intend to do more then waste our time." Magus smiled at this, replying "Yes, I think you will be interested in my discovery. To prevent a spacial fracture from occurring, we will need to jump through a few hoops, as it were. You see, closing the portal won't cause any issues. It is the removal of the connection between our and their world that ends with all the nasty side effects. Now, I few of you might think the portal and the connection are one in the same. That isn't the case, instead the portal is the entrance and exit while the actual connection is the road they lead too. So we should be able to close the portal here, then open a new portal here..." Magus pointed to a second circled location on the map, far into the mage guild in mostly unpopulated land. "...before the connection collapses in on  itself. If we can pull that off, then the new stable portal will not only allow the connection to remain but will ease the burden that the old one is causing. Over time, the strain on space should dissipate and we can close the portal for good. Any questions?" A female mage was the quickest, asking "Wait, if we are opening another of those portals then wont demons be able to come out of it as well?" "Yes, but they won't be in pain as they are now. While they can flow in, we will be able to choose where we open the portal." An older mage, who looked to be going for the classic white beard but could only manage a gray patchy fuzz said "So that is why you circled Venric's folly. Place is a canyon with steep walls a hundred and fifty meters high. There are less then half a dozen valleys in or out of there." Magus smiled, saying "Right, the area should function as a prison. We send one group to terraform and secure the area, while a second siege the research lab to close the old portal. The tricky part comes in the fact that we have roughly a five minute time limit between closing the portal and opening the new one. We must open the new one after the old has closed so a new connection isn't formed, but wait too long and the connection collapses in itself." Magus started assigning people to each group, with a fairly even split by then end. Magus and I would be with the terraforming group(Magus to open the portal, and myself because of my strange affinity with demons.) while the other three aid is the siege. Apparently closing that portal should be as simple as shutting off the generators for the lab and thus cutting all power to the portal. Because it sounds simple, it will be nothing of the sort. The demonic aura of the area seems to mess with some forms of magic, as an experiment we ran a few weeks ago showed. We shired this discovery when someone asked why Magus didn't just drop a meteor on it. "We tried that. Three times, in fact. All of them ended in different, strange ways." Answered Magus. Tiberius tacked on "First one was pushed away, and landed a mile from the target.", followed by Demir with "Second turned into blue cheese."(This got a few worried looks from some of the more experienced mages. Things turning into cheese was bad, but normally it was cheddar or jack. Blue was almost unheard of.) I added that the third was teleported away, and we still haven't found it. Vlad couldn't help but add "On an unrelated note, we haven't heard from the Redpower institute in a while." The general plan was for the group to brute force their way into the lab, locate the generators, then set up a foothold inside at the generators to rest at while waiting for the predetermined time. The terraforming group was to travel to Venric's folly and close off any entrances and exits, level some ground and change the biomes inside the depression in preparation for the ritual to open the portal. Most forms of communication would either not work, be too slow, or be unavailable to one or both parties so Magus opted for a simple time based system. This entire operation would take around two weeks; group one would need three days to prepair for the siege, a little over a week to get to the lab, then the remainder would be to entering to building, finding the generators, then holding off the demons by building a foothold to wait in. Group two on the other hand would have only a day to gather everything they could carry on their backs then regroup at Ank to ride the wagons for a ten day trip to the canyon to start working. After that, we need to build the ritual and hope to time it right... Then move on from there. Once the new stable portal is open, all bets are off as to what might happen next. The other mages have left to ready their things, and we leave for Ank in the morning. It has been a rough and bumpy week and a bit, but we have arrived at Venric's folly(I asked about the name on the way here. Apparently there is a legend about how the area was formed involving a mage named Venric and a large magical explosion. No one knows if it is true, but knowing mages I'd say it was possible.) The next few days are going to be filled with us working our magic on this area, smoothing out the walls to a near vertical surface and sealing off any and all possible exits to keep them in and fool-hearty travelers out. After that, we clear out an area near the heart of the canyon to build the ritual. Magus has requested I find a feral demon, something not to difficult all things considered, then bring it back to the ritual location. I'll try to write again when the ritual has been finished. It is the day before we open the portal, and Magus has brought us all together around the wicked looking structure he has built. "Tomorrow the peace ends and begins!" He cried when the mages had started gathering. "For the common man outside these walls, we will have brought peace and security to their lives, with the threat of demons ransacking their homes gone. For us however, we will have created a lot of bloody work. We will need people to keep an eye on these demons, examine the walls, and make sure nothing gets in or out. We will create a schedule, but that will come later as I know we are all weary. But this is not why I have called you here, as it is far more dire that a heinous error is corrected! I forgot to name this ritual behind me, with everything that has happened lately. Bella, can you come up here?" I walked up quickly, eyes of the fellow mages following me. "Bella, I give the honor of naming this ritual to you." In the space of seconds, it felt like I ran through thousands of possibilities. I finally settled on a name that seemed most appropriate. "Let us call it the Convocation of the Damned."
aw.entries.demons.observations=It has been a few days since we opened the portal, and I have noticed a few interesting things about the demons that have come out. First, they are already bound in service of other demons so they won't be willing to serve us. Second, these demons are just the grunts, the weaklings, the dime a dozen henchmen. Considering that they have proved to be strong enough to dispatch a skilled mage with ease... I worry about facing the higher ranking demons. And According to Sinferious, these grunts can come in one of five types: Fire(The ones we got, a portal seems to lock on to a set type), ice, earth, wind, and normal/untyped. Each one has some advantages and disadvantages over the others, so we will need to prepare based on what comes out of the portal.

aw.entries.spells.demonGirl=My life use to be simple. Get up at dawn, move with the caravan, keep an eye out for bandits, fight any that show up, do any odd jobs that needed doing, go to bed. For about ten years my daily routine was just this, with a few days a year where this cycle was broken. Living with traders was a hard life, but it wasn't bad. I gave up this life when my brother, Vlad, decided to take care of Bella, and I was given the choice of staying with the Merry Lamp or leaving with my brother. Wasn't much of a choice, really. He knows more details than I do, but soon after he found her the three of us were walking to the home of someone called "Magus Arcana". After we set up camp I asked my brother if he knew where we're going. I quickly learned that we didn't have much to go on. My brother had heard a few rumors, but nothing really concrete. With only the general direction of "To the lands of the mage guild!" we set off. So our adventure began, if a little out of the blue.  My name is Demir Highborn, and I am a blood mage. I have since earned the title of "The Battle Mage" to go with my brots "The Alchemist", my teacs "The Ritual Master", my friend Tiberius' "The Architect" and my adoptive sister Bellas "The Demon Kin". But I am getting ahead of myself, back to the beginning of our little "Journey". About two days after leaving the caravan, we stopped at a small town to get my brother a sword (We only had enough spare money for one) while I went to sell a few gems. While at the market I noticed two men in mage's robes, so if anyone was going to know where to find Magus here it would be them. I walked up to a vendor near them, neither of the two noticing me walk past them while they chatted. I traded the gems for a fair bit of coin, then asked the merchant in a voice a little louder then normal, "While I'm here, have you heard any rumors of a man called "Magus Arcana"? I have some business with him." The merchant chuckled and told me that he was nothing but legends told to kids to calm them down. Thankfully, he wasn't the one I was asking. Out of the corner of my eye I saw both of the robed men glance at me, then quickly turn back. They never would have made it as traders. I thanked him for his time, then started walking out of the market, down a narrow path under a few oak trees that had very few people. As I walked away, I noticed both of the robed men following me, just as I planed.  I slowly walked down the narrow path, giving the pair more than enough time to catch up with me. "Hey," one of them said as they drew close to me. I turned around, acting surprised that they had stopped me. "Why hello there. ' Didn't see you walk up." I told them, a simple trick to fool even mages that could tell when  you're lying. While it was true that I didn't "see" them, I still knew fully well that they were coming. The one on the right nodded, reading that I told them the truth. "We heard you asking about Magus, what business do you have with him?" The one one the left asked me. "Well, that is a little complicated. I need to find him so that he might teach my brother and I. I already know a little about magic, but people willing to teach for little to no coin are few and far between these days." The one on the right nodded again, then said "That is far too true. My brother Way and I were once apprentices to Magus, and he refused every coin we tried to give him. If you want to find Magus, head north east to a village called Lurric at the heart of these lands. We can't give exact directions, but I'm sure this is better then what you had. But fair warning, he tends to be reluctant to teach just anyone who comes knocking." I thanked them both, and they told me to say hello to their old teacher. Returning to the well, where my brother said to meet up, I found both Bella and Vlad waiting for me. "What took so long? You normally don't take this long to make a sell.", my brother asked. I told him what I found out. "I got more then coin this time. I got a name: Lurric. That is where Magus is suppose to be living, and we should find it if we head north east."  About three weeks of walking later, we arrived at Lurric. It was as lackluster as the rumors said, with very few signs that a mage lived around here. Vlad found one such sign while Bella and I went to find some antiseptic for the wounds he had gotten when a skeleton caught us off guard. He meet us with a grin on his face, then pointed us to a cobble stone path out of the village. After walking for about a quarter of a mile, we finally saw the fabled stone house of the great mage Magus Arcana. To say it didn't look like the house a mage would live in was an understatement. The outside was simple, with nothing really standing out that screamed "Magic!", so I became very skeptical of just how great he is if he lives in a place like this. It was smaller than the homes of some of the local lords, let alone most of the nobles. We walked up to the simple wooden door, the only possessions we had left were the clothes on our backs, Vlad's brewing stand, a few odd items that Vlad had collected over the years that we couldn't sell, and the sword we had bought a few weeks ago that had saved our lives more than once.  I wouldn't have let us in, let alone take us under his wing and teach magic. We looked more like street urchins then potential mages, in all honesty. When we walked up to the door, Vlad asked us both if we were ready. I told him I was, and Bella (Who had hidden behind me and Vlad) gave him a quick nod. With our blessing, Vlad knocked on the door sharply. After almost a minute the door opened, and a tall man answered us. Vlad talked to him, asking if Magus was here. Another minute later and a slightly taller man greeted us. I let Vlad do all the talking, watching as the man first seemed puzzled, then curious, and as he let us in he had a face of amusement. I forget most of what happened after that, mostly due to the fact that Magus had the other man make sandwiches. By the time I had finished my second sandwich, they were done talking. Magus and the other man left us, and I began talking with my brother. An hour later, Magus and his apprentice Tiberius (Vlad had filled me in on what happened while I was eating. I was almost hurt when he said that he wasn't surprised that I was more interested in the food than the man who gave us said food.) returned, gifts in hand. While Tiberius barely managed to bring in a large roll of fabric,  Magus came in arms loaded with more food. Once again, what happened next is a blur. The next thing I remember clearly was Magus and Tiberius creating new rooms for the house using their magic. No wonder it looked so normal from the outside, most of the building was under ground. With three new rooms and a hallway built, each of us had a room to call our own. This was odd for Vlad and I, as we had always shared one room (Normally with other people as well). Once we bathed and changed into the clothing Magus had made out of "Enchanted fabric", each robe dyed to a different color. Vlads green, mine red, and Bella asked for purple. Once we were all changed, we met for breakfast. It was a simple meal of waffles, syrup, milk, and eggs. While we ate, Magus asked a question: "Out of curiosity, do any of you have last names?" Vlad and I told him no. "We didn't need them as traders. The leaders always told us names got in the way of a sell." I said, before finishing the last of the eggs. He then turned to Bella, "What about you?" She also said no. "We... We got last names when we turned eighteen or took up a profession. Even so, everyone went by first names." Magus began to scratch his chin before saying "Well, most people around here have last names. Would you mind if I gave you one? You can't take Arcana, that name only brings trouble. So how about "Highborn"?" All three of us thought it over, then one by one we nodded with approval. Magus began to grin, saying "Very well, henceforth you shall be know as Vlad, Demir, and Bella Highborn." We spent the rest of the day and the next filling out our rooms. Magus provided furniture, supplies, and countless other thing to help us feel at home. Unfortunately, I had no idea what a home was suppose to look like, so my room ended up looking a little barren. I asked Magus if I could move a strange table I found in one of the storage rooms into mine. He told me I could, and offered me a hand moving it. It was a plain wooden table with a red cloth on top, a few strange symbols were faded in the cloth and in the wood. As I touched the table I felt something strange in the heart of the table: a strange power that wanted to escape the table.  I told Magus about it, and he was curious as well. "That is... Odd. I bought this a few years ago, intending to work out what it does. Feel free to take a crack at it yourself. Let's move it to the main workroom instead of your room then, alright?" We soon had it at one of the half dozen work spaces that doted the large room. This particular space was close to a white structure with an empty basin on top of it. 
aw.entries.spells.spellTable.1=It took me three days, but after looking into a few books I discovered gaining access to the power inside the table was going to be both easy and tricky. to get it out of the table I needed some sort of "Bridge", but I discovered there were two problems: Finding something that works as a bridge, and what happens next. I had no idea what would happen when the power was released. As far as I knew, it might be nothing or it might unleash hell on earth. I hoped it was somewhere in between. I quite literally stumbled over the answer to the first problem. At one point when I was walking out the back door to the strange structure so I could get a little air I tripped over the leg of a table, knocking off a  small box that popped open to reveal a small red crystal sphere. The box had the label "Failed attempt" written on the lid, and it also had a very sharp edge that I quickly cut my thumb on. As I picked up the sphere with my cut hand, I felt a strange sensation as I touched the crystal with my bloody thumb. Then the crystal started vibrating. It was soft at first, and as I carried it down to the work room it grew stronger and stronger. It suddenly  stopped when the crystal touched the table, and it seemed to flash briefly when this happened. So one half of the task was done, finding the bridge between the world and the table. My next goal was to see what happens when that power is released. With the permission and assistance of Magus, I went out a few hundred meters to conduct the test. With a simple nod from Magus, I began trying to tap into the crystals power. At first, nothing happened. Then I felt a burst of arcane energy, that then did nothing. "How odd..." Magus said as we walked home, defeated. "I felt the magic of the crystal as well, but it was almost without any form. There might be a way to shape it, so don't give up." While Magus left to help the others, I returned to the work room. Then I noticed something odd: The table was a few inches closer to the white structure(Which I later learned was a blood altar). On nothing but a hunch I used the crystal again. Once more the arcane energy went out, doing nothing. But I watched the table while this happened, and as I thought it seemed to move a little closer to the altar. With a quick push from me the table sat next to the altar, and the runes seemed to brighten a bit. I rested my hand on the table, and felt the power moving from the table into the altar as if it was seeking something. Going with my gut once more, I started searching for an item to use as a "Focus". After a few minutes I dropped a set of flint and steel in the altar, then went back outside to try again with Magus watching from just outside the door. After taping into the crystals, I felt a wave of warm air around me. It seems a focus is not enough, so I returned to search for something to refine the tables power even more. After an hour I found a few old skulls and heads of several monsters. After placing a skeleton skull on the table, I felt the table hum with power briefly. It was nearing dark, so with the last few lights of the day I tried the crystals power one last time. Magus watched near me as I tapped into the crystals power, and felt myself get a little weaker as a ball of fire shot out of the crystal. 
aw.entries.spells.spellTable.2=After showing Magus and the others my setup, both Magus and Tiberius seemed surprised to see that it needed the altar. After I showed them the crystal I had found, Magus and Tiberius asked me to hold off on experimenting with this further for a few days. Two days later I found out why. I don't feel like putting down all the details, so a quick summary: Each of us had unknowingly discovered new branches of "Blood Magic". Blood magic was a new form of magic that he and Tiberius had been trying to create and prefect for quite some time. Everything we had done so far had connected into blood magic in ways both of them had never expected. If we wanted to continue with the work we had been doing, my spells, my brots alchemy, and Bella's demon taming we would need to become blood mages like them. They went over the risks involved, told us that death was a real possibility, and that if we didn't want to go into blood magic we didn't have to. We quickly gave them our answer: We've already started on the path, so we can't really turn back now. We then went through the first test of any blood mage: The creation of a weak blood orb. All of our palms were tender at the end of the day, but we were now blood mages.
aw.entries.spells.simpleEffects.1=I discovered that the spells the table created used LP, and for the next month I searched for items to act as foci. Here is the list that I have discovered so far, but I don't want to write all the details, so anyone reading this will need to experiment on their own. As Magus says "Life is nothing without a few mysteries." 
aw.entries.spells.simpleEffects.2=Flint and Steel acts as, surprise surprise, foci for fire . As a result, most of the spells it creates will leave you or something else with a few nasty burns. 
aw.entries.spells.simpleEffects.3=Ice blocks seem to make the effects far chillier, slowing what the projectiles hit and forming ice.
aw.entries.spells.simpleEffects.4=TNT as a foci is quite... Explosive. All of the effects, in one way or another, end with a bang.
aw.entries.spells.simpleEffects.5=Ghast Tears let it control wind, pushing entities around.
aw.entries.spells.simpleEffects.6=Glowstone Dust governs the weather, allowing lightning and such to happen. 
aw.entries.spells.simpleEffects.7=Bucket of Water has to do with the control of water, strangely. Expect drowning and water, not necessarily in that order.
aw.entries.spells.simpleEffects.8=Obsidian has the energy attune to stone, for some... Interesting effects.
aw.entries.spells.simpleEffects.9=Ender Pearls allow teleportation, for both you and others.
aw.entries.spells.simpleEffects.10=Apprentice Blood Orb create some strange combat effects that have little rhyme or reason from my experience.
aw.entries.spells.simpleEffects.11=Something else that is interesting about the table is that it does not need to be re-bound to the crystal. If you change the foci or the paradigm the spell will change as well the next time you use the crystal. I also found four "Paradigms" to use with the spells. Skeleton heads imprinted a ranged attack to the energy of the spell, the wither skeleton skull made the spell more suited for close combat, the zombie turned the spell defensive, and the creeper head gave the spell more environmental impact. I should also note that Magus shared with me crafting recipes for turning wither skeleton skulls into heads of the other mobs. Seems a bit pricy, but when you have no other choice...
aw.entries.spells.tableAndSkulls.1=I asked Tiberius how he made the spell crystal, and he told me that while trying to create a new blood orb he stumbled across it. All it needed was a piece of glass in a blood altar, using 1k LP to transform the glass into the crystal.
aw.entries.spells.tableAndSkulls.2=We managed to recreate the powers and effects of the "Spell Table" using blood magic. Amazing what you can do with items laying around Magus's home...
aw.entries.spells.timePasses=This brings us up to today, when Tiberius brought us books so we could write all our work down, if we wished. It has taken a few hours, but here we are. Currently I am working with my brother to create a much more advanced form of the spell system, and will add any information I learn to this book when I have time. Has it really been three years since we came to this place? My life before coming to Magus seems like a distant memory now, and my days have become filled with training and work that I have fallen in love with. Since I last wrote here so much has happened... My sister Bella has come out of her shell, freely walking amongst the villagers in the market without cowering behind us. She has become more outgoing, helping Magus deal with demon attacks and cutting the number of deaths every year substantially. She has formed pacts with over a dozen demons, while working with Magus and my brother to create a way to "Pull" demons into our world, body and soul.   My brother Vlad has been busy with his alchemy, creating new potions that have powerful effects but more often then not taste like tree bark mixed with dirt. Alongside that he has been helping me create the components I need for my spell constructs. I needed materials that conduct large amounts of magical and elemental energy, and he has been the only reason I've gotten so far. And no, I'm not writing that just because he is sitting next to me, though it would be a lie to say that hasn't been a factor.
aw.entries.spells.complexSpellBasics.1=Anyway, back to what we're here to write about. We have discovered a way to use the items known as "Blood Shards" and my brots alchemical reagents to create a new way to form spells using blood magic. It uses four components: Paradigms, Effects, Modifiers, and Enhancements. The main improvement of this system over the old one is the ability to "Chain" spell effects together. Before I cover how to create the spells, I should go over the parts.
aw.entries.spells.complexSpellBasics.2=The paradigms act as the starting point for the spells, and define what the spell will target. There is only one "Output" side to these blocks, and it is on the side with five points(It will be hard to miss it), and the output side can be rotated with a simple right click. We have currently created four different paradigm versions: Projectile, melee, self, and tool. While the names feel self explanatory, I'd best talk about them anyway.
aw.entries.spells.complexSpellBasics.3=Projectile: Makes the spell ranged, and will trigger on impact with anything. 
aw.entries.spells.complexSpellBasics.4=Melee: Short range spell, normally the target must be within one to two blocks for the effect to work.
aw.entries.spells.complexSpellBasics.5=Self: Targets the caster, or the area immediately surrounding the caster. 
aw.entries.spells.complexSpellBasics.6=Tool: Creates an alchemical manifestation of the spell from the crystal in the shape of a mace. Depending on what is in the spell, it can change what this mace does, from being a weapon or a digging tool or "Crushing" any block it breaks.
aw.entries.spells.complexSpellBasics.7=Next we have the effect blocks, and these are what give the paradigms some kick. Unlike the paradigms, theses have both an input and an output. An effect blocks input is always the five pointed non-colored side, while the output is the side with the colored ends(The color depends on the effect block). We currently have the effect blocks for fire, earth, wind, and ice. The effects of these blocks are heavily influenced by the modifiers that come after it, but spells can function with only a paradigm and one effect.
aw.entries.spells.complexSpellBasics.8=Modifiers change what the last effect in the chain before them does. So, if you had a fire, ice, wind chain then placed an offensive modifier, the wind would take an offensive effect while the others remain default. The current modifiers are: Default, Offensive, Defensive, and Environmental. 
aw.entries.spells.complexSpellBasics.9=Default makes the effect act as if you left it without a modifier. Useful for spells where you want to guaranty its effect.
aw.entries.spells.complexSpellBasics.10=Offensive makes the spell more combat oriented. Doesn't mean the spell will deal damage, but it might help in combat.
aw.entries.spells.complexSpellBasics.11=Defensive has shown to change effect to protect the user. This comes in several forms, from creating shields and cloaking the user to slowing an opponent.
aw.entries.spells.complexSpellBasics.12=Environmental does what the name says: It makes spell effects have  bigger impact on the world, from setting fires at the targets feet to carving out large chunks of land.
aw.entries.spells.complexSpellBasics.13=Finally we have enhancements. Enhancements improve the spell without major changes to the effect. The enhancements are: Power, Cost, and Potency. Like most other spell blocks, plain is in and colored is out. What should also be covered are the frames that hold enhancements. There are three types of frames, each stronger then the last. Your cheap and basic unstable frame can only withstand one per type(You may have one power, one cost, and one potency. If you try to add two unstable power enhancements one will not be able to improve the spell, and actually might blow up damaging the blocks around it). While you may only have one unstable per type, you may have two standard and three reinforced for a grand total of three enhancements of any type(If you want to save on the resources needed for these enhancements, try having one of each in the chain, placed in the order of weakest to strongest. While an unstable can only handle its own power, a standard can handle its own and that of a unstable, with a reinforced able to channel all three without failing). As I write this down I realize it is a little confusing, but with a little practice it becomes simple.
aw.entries.spells.complexSpellBasics.14=Power boosts the, well, power of the spell. If you had a spell that did damage and gave the target a potion effect, the spell would do more damage or make the potion effect stronger.
aw.entries.spells.complexSpellBasics.15=Cost lowers the cost of the spell effect. Doesn't get much simpler then this, yet will always be useful as the cost of these spells can get quite hefty after you chain a few effects.
aw.entries.spells.complexSpellBasics.16=Potency increases the potency of the spell. What this means differs from spell to spell, but with our damage/potion effect example it might make the effect last longer or be stronger.
aw.entries.spells.complexSpellBasics.17=This covers all the parts of a spell, but there is still one block remaining: The conduit. It doesn't add anything to the spell, but it allows you to have the parts of the spell farther apart. The input and output is dictated by the dots on the respective side. Four small dots is the input, one large dot is the output. Now then, how do you create a spell? First, pick your paradigm. Then, create a complex spell crystal to bind to the paradigm(Crafting recipes will be on the following pages for convenience) and make sure the crystal is also bound to an owner. Then, place and connect an effect block to the paradigm(Change outputs with right click, inputs with sneak right click. Do note that one side can't be both the input and the output.) Now the spell will work, but it might not do what you want. You can alter the spells effect by placing down and connecting a modifier. Still doesn't mean the spell will work as expected, but you can at least nudge it in the right direction. Finally you can add enhancements to boost the spell, but they might make the effect cost more(Unless you add cost enhancements), There are three tiers of enhancements, and a spell effect can one tier one... You know what, I went over this in the modifiers section. Look there for more information. But you don't need to stop on one effect, you can chain to another effect and another effect and another.... The possibilities are as endless as your soul network will allow. A few final notes: Every spell will do something. Some need other effects to really flourish while others work best on there own.  The cost of the spell will always be the sum of all the effects and their enhancements multiplied by the complexity factor. So you can create massive spells with several effects at once, but watch your soul network as you use it. Just a warning.
aw.entries.spells.crafting=
aw.entries.spells.complexSpellEffects.1=And now Vlad is requesting I mention the effects of the spell... This might take a few pages, but I guess it is worth doing.
aw.entries.spells.complexSpellEffects.2=Self paradigm effects are always interesting... A default fire effect causes the user to burst into flames, while an offensive effect cloaks you in fire to ignite attackers and nearby entities. Defensive forms a puddle of magma at your feet(Useful if you can't be hurt by fire, but the thing trying to kill you can). Finally for fire, environmental causes you to superheat an area causing fires and things to change(Sand to glass, cobble to stone).
aw.entries.spells.complexSpellEffects.3=Earth is a bit less...Wild in it's effects for self. Default raises the ground bellow you up to your feet, forming a platform to stand on and I have found it helpful for when you suddenly don't know where the ground has gone(Happened once when testing a wind spell). Offensive creates an impact at your feet that crushes blocks, cobblestone to gravel, gravel to sand... Defensive effect is a powerful combat option with a catch: You become much heartier, able to take far more damage then normal at the cost of movement. Environmental creates a quick shock wave that weakens near by entities. 
aw.entries.spells.complexSpellEffects.4=Ice has been a fun one to use. Default creates a small pillar under you that pushes you up, allowing for quick vertical movement. Offensive causes entities around you to become lethargic, moving slower. Defensive is a bit more interesting, allowing you to slow entities and freeze water using a cloak of ice(You can easily use this to walk on water). When water isn't available, you can create a bridge of ice using environmental ice.
aw.entries.spells.complexSpellEffects.5=Wind has turned out to be quite practical in it's uses, from my tests. Default hs the useful effect of creating a brief burst of wind that puts out fires on the user and protects the user from sudden downward momentum that can cause quick yet extreamly messy death. Offensive mimics the effects of an air sigil, launching you forward. Defensive causes near by entities to have a strong downward wind limit movement. Finally, environmental causes a large high pressure area to form around the user, that then moves to the low pressure area(Everywhere else). This has the side effect of taking entities with it in the sudden gust of air that is formed.
aw.entries.spells.complexSpellEffects.6=On to melee effects, for those who like close range combat. Starting off with fire again, this effect as default causes spontaneous combustion for near by entities. The always entertaining offensive effect causes the target to gain an effect called "Fire fuse". This effect does nothing until it expires, at which point the entity explodes. The explosion not only damages the entity, but causes it to to launch in the air while the explosion damages near by entities. Defensive allows you to wall off areas with fire, stopping things from passing unless the brave the flames. Environmental causes it to form a short range projectile that evaporates any water nearby.
aw.entries.spells.complexSpellEffects.7=Earth melee has a few of the old effects in a new form. Default destabilizes blocks, causing them to fall akin to sand. Offensive has the same smashing effect from earlier in projectile form. Defensive forms a wall by raising a chunk of the ground up. Environmental on the other hand is able to remove the stone from an ore, leaving a fair amount of dust to process into ingots.
aw.entries.spells.complexSpellEffects.8=Ice allows some useful combat options, starting with a default effect that allows the target to lose the tolerance to pain that happens after a blow effectively allowing you to apply more damage in less time. Offensive rockets the target into the air akin the the self ice effect from earlier. Defensive forms a wall of ice to protect you from damage. While Environmental... Well, it is odd. It unleashes an omnidirectional volley of  snow balls at the target. 
aw.entries.spells.complexSpellEffects.9=Wind is very combat flow oriented, with default pulling entities towards the user,  offensive away from the user, and defensive in the air. Environmental is more utility, as it pulls items on the ground toward the users.
aw.entries.spells.complexSpellEffects.10=Projectile spells share many of the effects of other types with a few all their own. A default fire allows the attack to unleash a torrent of fire when it hits something. Offensive creates flowing lava in the air under the target, but is tricky to use without the aid of other spell effects. Defensive smelts blocks, while environmental evaporates water. 
aw.entries.spells.complexSpellEffects.11=Earth projectiles are mainly focused on  mining, with default disintegrating all matter it comes in contact with. Offensive forms a hole under an entity it hits(It will normally only dig the hole in mundane blocks like dirt and cobblestone). Defensive smashes blocks in the same manor stated before, while environmental mines blocks in an area around the projectile.
aw.entries.spells.complexSpellEffects.12=Ice has a lot of classic effects, with default being an ice plume on impact, Offensive slowing targets, defensive freezing water and making snow, and environmental just freezing water in an area around the projectile.
aw.entries.spells.complexSpellEffects.13=Wind has a few interesting effects, with default sending mobs flying, offencive giving "Heavy heart" to ground entities, defensive letting blocks mined by the spell drop as if mined by a silk touch pickaxe, and environmental pulling items to the user.
aw.entries.spells.complexSpellEffects.14=Tool spells seem to be a mix of utility and combat, with many of the effects allowing it to function as a set tool type. Starting with earth, default acts as a pickaxe, offensive as a way to remove mundane blocks, defensive a shovel, and environmental gives it the power to mine multiple blocks. I know I sound like I'm rushing through these, but they sort of say what they do on the tin.
aw.entries.spells.complexSpellEffects.15=Fire has a few powerful effects, with default smelting anything mined(Cobble to smooth stone, sand to glass...), offensive acting like the enchantment fire aspect, defensive increases the time the spell stays in tool form, and environmental lets it turn stone into lava.
aw.entries.spells.complexSpellEffects.16=Ice is more combat centered, with default letting blows it deals slow entities, offensive giving it a few sharp points for more damage, defensive unleashes a torrent of cold air when the tool is summoned that creates snow and slows mobs. The environmental effect is a black sheep, allowing the tool to function as if it has silk touch.
aw.entries.spells.complexSpellEffects.17=Wind has effects that are just... Useful.  Default allows it to function as an axe, offensive gives it some extra punch to throw mobs back after being hit, defensive weighed them down, and finally environmental pulls items closer to the user. 
aw.entries.spells.offTopic=Tiberius has informed me that this book will update as I add to it, so if we ever create more blocks for the advanced spells, you'll know when I do. For now, this is the end. And yet it might be your beginning. I'll go off topic for a bit, because today something happened that I never thought possible: I killed a wither(You know, big black flying skeletal death monster that wants to kill everything? One of those.) Not only did I kill one, but I also helped kill twenty more. Magus said that the most killed at once before this was ten by three people, so we crushed the record.  We needed a few nether stars to upgrade our altar, but nether stars are near impossible to get. Unless you work on the conglomeration board like Magus that is.(He runs the largest country in the world, apparently. I know, I can hardly believe it either.) After inviting every single world leader for a holiday weekend(Of which they actually treated like a holiday trip), Magus managed to get us a reservation in a battle arena to kill thirty withers. To say I was shocked is an understatement. But, by some miracle, we managed to not only kill all of the withers(And all of us lived to tell about it). Sadly, I'm stuck in bed with a busted leg(Wither skull nailed me when I was casting a spell.), so I've taken this as an opportunity to try new kinds of food(Magus knows how to cook like any five star chief). I'd keep writing, but I smell the roasted chicken coming down the stairs now...  Vlad has finally found a more stable recipe for the more advanced spell parts for the augments. While he has been able to create them before, the new orbs we have allow them not to fall apart four out of five times. A slight improvement, I'm sure you may agree. With this, the reinforced spell augments are much more reliable. Well, the last few days have been interesting. We have managed to open a hole in the fabric of space to let in an invading army of demons, but it was to stop another portal letting in other demons so it is all fine. Sorry, but I had to comment on how ridicules that sounded when I first heard Magus's plan. But it isn't as bad as it sounds, since we opened the second portal we had control over where the demons come out and can prevent them from running wild. Absurd sounding, I know, but when you deal with holes in space leading to demonic realms things are rarely ever simple. Or when a mage has any involvement, for that matter.
aw.entries.spells.demonicPower=Gah, look at me! I opened this musty old book to mention the new augments Vlad and Tiberius had created using the spoils of this "War", and ended up ranting about how strange and wonderful life as a mage can be. Right, Vlad managed to make a few parts better using these life and soul shards we've been collecting. Not much else to it, really. Sure, Vlad might say how he had to create new laws of alchemy to explain this process, but he does that on a daily basis anyway.

aw.entries.alchemy.fatedMeeting=My name is Vlad Highborn, and I am a Blood Mage. I have studied the intricate workings of alchemy and the process of "Equivalent Exchange," which governs all aspects of magic. Basically, you cannot create something from nothing, although many have tried when searching for a particular stone. That obviously didn't end well, because people are clamoring for a fake variant even today. Of course simply saying that I am an alchemist isn't enough, because one of the main things I do is study Blood Magic with The Ritual Master and The Architect, both of whom have achieved those titles by their own merits. Magus and Tiberius have been busy recording their own works over the years, although I don't think Magus has everything written down in a book - I have yet to find any actual proof. Demir and myself were working with a trading company named the "Merry Lamb," which operated throughout the entire lands travelling from place to place, selling our wares from horseback and buying whatever was of value. We didn't have last names, we didn't need them - the only thing that was of worth to the caravan company was how much of a profit you were able to obtain, and last names only got in the way of a sell. When we weren't busy with the various villages, trying to part with our gleaming swords and precious gems, all that was left for us was to read the many books that we obtained in the light through the canvas of the caravan. That was how I fell in love with Alchemy in the early days.  One of the members of the trading caravan I worked with was an alchemist, who created simple potions and poisons for both medical use and profit. He taught me many things while he worked with us, but I quickly learned that he only knew the basics of what could be done. This void of knowledge led me to collect several alchemical tomes from the merchants of the passing villages, where I heard of a powerful mage that lived on the outskirts of a small village. At times I fantasized of meeting him, perhaps even learning from him alchemy like nothing I could ever dream of. But every rumor made me think he was more myth then man, filled with outrageous stories of his skill and power that no one else has ever came close too. Paired with the fact that no one had ever seen him almost killed the last of my dreams.  I was reading the latest book in my collection, one that I bought from a hermit alchemist in the last village who kept muttering about turnips, when I noticed a flickering that didn't match the light of the candle that I had in the caravan. I looked out of the back of the caravan as it slowed to a crawl, all of the traders were looking at the same thing: a trail of dark smoke rising from behind the trees and blowing in the harsh wind. The head of the caravan quickly dispatched a group containing one person of each profession, which included myself as the now only alchemist, and we headed towards where the village used to stand. A massacre. I saw the smoke and ashes of the buildings, charred and even frozen flesh littering the ground. I swallowed heavily, looking around me for hope that I wouldn't lose my supper, and overheard someone speaking the obvious, "It must have been demons! No mage would use both ice and fire to attack a village." "Hell, no self-respecting mage would attack a village in the dead of night. They still honour that code from the medieval era." We readied our weapons, myself relegated to medic duty, but there was hardly any use because demons rarely stayed for more than an hour after they're done. "Hey," Nick shouted, a new member to our group, "there's someone here!" We all looked around and saw a little girl, possibly about age twelve, wondering through the ashes and looking both lost and dazed. "The demons leave no survivors, boy," the leader of our group said. "It is a bad omen to find her here, alive and alone. Hell," he laughed bitterly, starting to walk away, "that may not even be a ' - could be another demon for all we know." The thought stirred me, not of what she could be but that someone could leave a lost child by herself. "Fine," I said, after arguing the point for several minutes, "if you won't take her, I will leave the Merry Lamb." I can't fully remember the argument because of how much time passed before I could finally write it down. What I do remember is that the head of the caravan was quite startled at my proclamation, but finally agreed to send a carrier to fetch both my brother as well as my belongings, and to give me a good deal on some supplies. "I won't try to talk you out of it, since I know how you get when your mind is set. But if I may ask, why are you so intent on trying to help her?" I turned around to start walking towards the girl, who was watching us blankly. Not surprising, considering we are probably the first people she had seen today. Living, at least. After walking the few paces that put me between the girl and the caravan, I turned to the leader and said what I was holding onto for the past few years. "I know what it is like, to not have any home to call my own. I'm hoping that this is not a bad omen, but is rather a sign of fate." Unfortunately it might have been a little preemptive of me to throw away my whole livelihood and wayward home to help a single lost girl. Demir wasn't exactly happy having to pick up everything that he had to head out on an adventure and neither of us were expecting, but I couldn't really blame him. Demir was attuned to several forms of magic, being the resident wizard-in-training of our group. Thankful for the little spark of mana that he managed to cultivate over the years, he kindled a fire for the three of us once we managed to walk off the rest of the night.  Her name was Bella, with light blonde hair and blue eyes. When we asked her what happened that night, she simply shook her head and tucked her knees under her chin. Demir and I looked at each other, and we knew that no form of magic that we knew would heal her of the wounds that she had in her mind. When we tried to ask her if she knew of any magic herself, considering that we neared the boarder to the Mage's Guild's land, she shook her head again and looked sullenly back towards the village that she came from, now miles behind us.  Demir finally asked the question that I was thinking myself - it seemed that he always had the uncanny ability to read my thoughts, or at least my mood at any given moment. "So, which way is it to this 'Magus Arcana?'" It was a long and difficult trek towards the village that the Mage was rumoured to live in. Three long, blistering weeks. It is actually quite amazing how a distance can be so hard to traverse on foot without a caravan, pulling our dwindling belongings with us as we continued onwards towards our goal. When we reached a village we would do the same thing: we would go to the local market at daybreak and sell whatever books and gems that we could, grab our allotment of food for the next leg of the journey, and ask in the tavern if anyone has heard of rumours of the great mage Magus. It seemed that with each village our supplies were getting less prosperous, dwindling until all that was left was a few worthless rocks that no one was keen on buying. Thankfully it seemed that we were getting close, since the rumours were becoming more and more substantial, until it became a reality. Passing by a blacksmith's shop, I noticed that the furnace was burning a lot more brightly than if it was filled with lava, and I peered into the slot for the fuel. What appeared to be there was a crystal made of lava, pulsating slowly as if it received a beat from a heart nearby. I asked the blacksmith about it, and he told me about an apprentice who made it, who didn't appear to have a name in the blacksmith's memory - if I were to hazard a guess, I'd say that he was more afraid of the apprentice than he let on. Grabbing Demir, who bought some antiseptic for a few wounds we received from a stray skeleton the previous night, and Bella, we finally headed towards the outskirts of the village we were looking for. The home wasn't hard to find, there was a simple cobble stone path leading away from the village to a lone stone house. Considering the fact that there can't be that many homes out here like this, this was the place or whoever does live here can give us directions to the right one. We walked up to the surprisingly simple wooden door, and I turned to the other two. "Are you ready?" I asked them, more for my sake then theirs. Bella simply nodded, barely speaking more then a few sentences a day since we left three weeks ago. Demir put his hand on my shoulder saying "This is the place, right? Let's not just stand here like a bunch of geese." Gathering myself, I knocked three times on the door. At first, I feared we had the wrong house. Then I feared we had the right house, but no one was home.... Before my mind could think of another scenario, I heard the sounds of a door being opened and someone walking on stone. The door swung inwards with a light squeak from the hinges, reveling someone of about eighteen wearing long robes. "Hello, how may I help you?" He asked after quickly looking us over, and I assumed this is the apprentice the blacksmith mentioned. "We're looking for the great mage Magus Arcana, is he here?" The man looked at all of us, then turned his head yelling "Master, there is someone here to see you. They're asking for you by name." We soon heard a door open again, and a man who looked to be in his late thirty's appeared behind the apprentice. I saw him briefly look the three of us over, raising his eyebrow as he did so. "You must be Magus. We've come a long way to meet you. May we come in?" He seemed to think this over for a few seconds, then sighed saying "Alright, you may come in. Leave your boots at the door, then follow me to the main room. Tiberius, can you make a quick lunch for the five of us?" The apprentice nodded, then left around a corner to the right while Magus lead us left to a large room with a few chairs, a simple couch, a plain wooden table, and an unlit fireplace with a strange red rock on the bottom. He gestured for the three of us to take a seat on the couch, which we gladly accepted. We had been on our feet for the last three weeks, so a chance to sit in a cool room was a nice change of pace. As Tiberius brought out a tray with several sandwiches on it to set on the table, I looked around the room to see what was on the walls. Stone tablets with strange symbols, brightly colored wands and stave's adorned the walls like kings use old swords, and a small book shelf was set above the fire place holding a variety of books in a few languages. I recognized "The theory of equivalent exchange" by Pahimar, the book that sparked the search for that certain stone. I noticed that while I had been gawking at the walls, everyone else had started eating. I grabbed the remaining sandwich, a simple pork and cheese with a strange sauce that I didn't recognized but immediately loved. "So..." Magus said as we finished the last few crumbs of the meal. "..what brings you to me?" Demir gave me a sideways glance, my que to start talking. "Well, I heard stories of what how much you know. I wanted to learn alchemy, so I came here. I know of only one other alchemist, the one who taught me the basics." He frowned, pinning his glare on me. "I looks like you three traveled a long way to get here, and you were unprepared for a long journey. So I'll ask again, what made you come to me?" He knew we didn't have as much of a choice as I was trying to let on, so there was no point in lying. "My brother and I lived with traders. About three weeks ago we came across a village that was attacked by demons, with there being only one survivor." Magus glanced over at Bella, who was eating a second sandwich Tiberius had brought for us. "Yes, Bella was that survivor. That other traders wanted to leave her for dead, but I couldn't bring myself to do that. So I was forced to go threw with a plan I had been preparing for the last few years, trying to find you, early. I had originally planned to come here alone, and had just enough money to get one person here. We barely scraped by with three, getting here with the last few gems I had to trade." Magus nodded, a intense look on his face. "And what were you planing to do if I was not here, or you never found me, or if I was a myth like many people think I am? Or did you even have a plan?" I sighed at his questions, he really pinned me down and read me like a book. "I had a back up plan, if a bad one. If worse came to worse I planed on trying to find a village that needs an alchemist to brew potions. I still have all the equipment in my bag, one of the few thing I couldn't bring myself to sell. I know how much the traders made off my work, I could feed the three of us with it, if only just barely." Magus nodded as if I gave the right answer. "Now then, what of your brother and your friend Bella? What had you planned to do with them if you found me?" "Well, my brother has a little mana, enough to create sparks strong enough to light fires. As for Bella, she doesn't know if she as any talent for magic." Magus's gaze shifted over to Demir, then Bella, then returned to me. "And if your brother or Bella didn't have enough talent for me to teach them?" I sighed once again. He was asking all the right questions. "I would shift to plan B. I'm not leaving my brother or Bella, not after all we have been threw, and not for the teachings of a heartless mage." He gave me a hard stair for a few seconds, then a wide grin grew on his face as he said "Right answer. Alright, I'll see what I can do. I should be able to teach something to you three, and I doubt anyone else around here is going to take her in. Rumors of the blond haired demon girl have beat you here." Magus made a few arrangements for us, having Demir and I stay in Tiberius's room while Bella took Magus's. When I asked where he would be sleeping, Magus said he kept the old couch around for more then one reason. When I started to protest he said that this was only temporary and that the three of us would have our own rooms tomorrow. When I asked how, all he did was smile and say "You'll see." While the three of us got accustomed to our new home, Magus and Tiberius went out for a few things. "Well, there an odd bunch." Demir said shortly after they left. "Don't laugh, they can say the same about us." I told him. This comment earned a rare smile from Bella, who had slowly started coming out of her shell. Magus and Teberius returned a little over an hour later, Magus caring a huge amount of food while Tiberius had a large roll of uncut fabric. Magus brought the food to the main room we sat at before, its intoxicating smell had caused both my brother and Bella to start drooling. I would criticize them if I wasn't doing the same thing. "Even after lunch you three looked half starved." Magus said as he set down loafs of fresh bread, cheese, and dried meat slices. "So we got you something, though we didn't have many options on short notice. Consider it a welcoming gift, along with some new clothes we'll make. But those can come later, for now you can dig in!" We ate like wilder beasts, tearing apart the bread, clawing for the meat, and making sure to get every last crumb of the cheese. I had no idea just how hungry I was till I saw all that food, and by the end of it I was amazed at how long it had been since I ate like this. Three weeks ago? No, a few month ago. When the traders were celebrating a huge trade with a large and wealthy village. The leader has so happy that he almost spent half the money he earn off the deal on the party. Almost. After a nice long night of sleep, we all awoke to the sounds of Magus and Tiberius carving out a large chunk of the dirt on the other side of a wall. I was fascinated by how they were doing it: Magus cast a spell to remove the dirt, then Tiberius replaced the dirt with stone bricks using a strange looking wand. At first I wondered where all the dirt was going and stone was coming from, then I noticed a few strange bags at the side of Magus and Tiberius. Once the main size was laid out, Magus asked Tiberius for the bag he had. With it in hand, Magus raised one arm to have walls raise out of the ground with it. Then Tiberius grabbed a few strange flying flames to light up the hallways, and candles for the rooms. "Alright, we're done. You three now have rooms to call your own. I'll have to do some work, but we might be able to get a few beds here as well. I only have one spare, so you three can decide who get's it for now. "Bella." my brother and I said in unison. "Alright. Tiberius, can you give me a hand moving it to her room? Also, your rooms have a small work area. If you need more space, you can use the main basement area or let us know. We'll see what we can do. For now, you three can pick which room you want, then move unpack your things there."   The next two days went by fast, Magus helped make our rooms homes and assisted us in finding fields of magic to study(He had over a dozen tomes on alchemy inside is huge library, so I quickly felt at home with my nose in a book). While he attended to Demir and Bella, I started diging into the tomes. After cross referencing several of the books, I noticed that very few ever tried to improve upon the first brewing stand. Of the few who did, none succeeded in making potions larger then one dose. After briefly talking with Magus and Tiberius, they encouraged me to try and improve the design and gave me a space in the main workroom to do my testing. 
aw.entries.alchemy.firstSteps=I knew I needed to try and reinforce the stand, as it was far to fragile to mix more then one item at a time. I tried iron, gold, stone, and several other material. They ether were far to weak, incompatible with the stand, or unruly with my attempts to shape them. It was when I tried to use obsidian, something that was compatible with the stand, strong enough to withstand the strain of the alchemy process. My only problem was fusing the obsidian with the stand. While I pondered my options, I felt my hand brush against a strange red orb that had been sitting next to my work space. Once I did so, the obsidian seemed to spring to life, wrapping around and shaping the stand into something far greater. I called down Magus and Tiberius to see what just happened. When Magus asked how I did what many others failed at, I told him the truth. "I'm not sure. I was thinking of how to do just this, when I accidentally touched something on that table and then the obsidian sprang to life." "You touched something and the obsidian seemed to come to life? What did you..." Magus said, then stopped dead as he looked at the other table. "Was it that orb?" He asked hastily. "I think so. After I touched it, this happened. It looks like what I created had a slot shaped like that orb."I told him, and Magus looked shaken. "Tiberius, we're going to need to talk about this. For now Vlad, can you hold off on testing what that device does?" "Alright." I told him, a little confused. "But I think I should name it. How does "The Alchemical chemistry set" sound?" Magus put on a weak smile "That seems fitting. It seems to be much more then a simple brewing stand. I recall Demir saying you had a few alchemical items that an adventure sold to the traders in your possession. Perhaps you can try to work out what they do, or what they're made of." Trying what Magus suggested, I started experimenting with the strange alchemy items I collected in my time with the traders. In total, I had two small red orbs, one larger red item that seems like a better version of the red orb, three small blue orbs, and two blue items that seem like they're between the small orbs and the large ones. Using a small alchemy lab Magus brought me to, I started breaking down anything I had spares of. Taking one of the blue orbs and grinding it in a mortar and pedestal, I found it was made of three redstone, and two items I had never seen before. One was acting as a "Binding" agent, while the other was some sort of liquid catalyst. After a few hours of work, I had a few formulas for the items, but I still needed to recreate them. Normal mixing wasn't working, as it seems like there was some other factor that merged them in the first place that I am missing. I'll write the formulas here when I find out what I'm missing, so until then I start grinding up the other items. Alright, after about three days worth of work I now have discovered that my original assumption that the larger red and blue reagents were stronger, more "Condensed" versions of the small orbs was correct. After a bit of work I am starting to think there is some sort of link between the alchemical chemistry set and these reagents. The chemistry set has five "Input" slots and one "Output" slot, and many of the items here need just five items... I will need to ask Magus about trying this with the orb that formed the set inside. Well, I am apparently now a blood mage. Magus and Tiberius told us details about some of the items we had been finding around our new home, from the weak blood orb that help create the alchemical chemistry set(Which Bella later used to tame her new demon friend. After everything I have heard about them, he is remarkably civil.) to the altar behind the house and in the basement. I was given the choice of continuing to study blood magic and use my new chemistry set(And taking all the risks that come with it), or studying other forms of alchemy. I went with the risky option, simply because I would be blazing a new form of magic. I now have a weak blood orb to call my own, and I will start trying to experiment with creating some of the reagents I have discovered in the last few days.
aw.entries.alchemy.chemistrySet.1=Hmm. It seems that the "Simple Catalyst" is the only alchemy reagent I could make with a weak blood orb. Once I had the recipe(Two parts redstone, one part glowstone, one part sugar, and one part gunpowder. Mixing these together should make one simple catalyst), I place one of each item in the outer five slots of the chemistry set. This alone is not enough to create a simple catalyst, you need to have a blood orb bound to you in the "Orb" slot to supply LP for the process(The simple catalyst only needs 200LP, so it is fairly cheap). Tiberius mentioned he had already made a stronger blood orb, so I might ask him for help making my own. I'll log any recipes I find. With a bit of work, I have managed to create the other component of the tiny dark blue orb(The small light blue orb still eludes me. I might need to look into a stronger blood orb, but that will need to come later.) The "Binding Agent" seems to have a few uses in both crafting and the other experiment I have been running with potions(More on that later). The weak binding agent needs only two simple catalysts and a piece of clay to form(Along with 1K LP). I say weak because I feel I can improve it with some of the reagents I have been creating. For now, I should go over what I've made in the past month.  With the help of Magus, I have managed to make "Alchemical elemental reagents". These reagents represent various elements found in the world that always play a major role in both magic and alchemy. I will make a quick list of each element and its recipes, and will try to create these entry's as close to what I find in all the alchemy books I have read. Also I should note that they all need 500LP as a catalyst to make.
aw.entries.alchemy.chemistrySet.2=Incendium: Holding the power of fire and the nether, incendium need items often  attributed to fire and are a part of the nether. Due to this, it is very hot and it has left me with a few small burns from my time handling it. 
aw.entries.alchemy.chemistrySet.3=Aether: A reagent holding the power of wind, I have had to make sure it doesn't float away with every breeze. Made with items that seem to always be used as in reference with the wind(Ghast tears and feathers) that I bound to glowstone dust and a simple catalyst to give it some physical form.
aw.entries.alchemy.chemistrySet.4=Sanctus: Closest thing to powered light I could make, it seems to hold strange properties that I still can't understand. It needed a little gold, glowstone dust, and glass I had ground up in the chemistry set to  make, but it seems worth the cost.
aw.entries.alchemy.chemistrySet.5=Crepitous: Chaos and destruction in physical form, I named it after the creepers that gave me the idea(And the gunpowder) for it. Made with one simple catalyst, two cobble stone, and two gunpowder.
aw.entries.alchemy.chemistrySet.6=Crystallos: A fine, cold powder that sends chills down my spine every time I touch it. Made with two parts ice(I need to find a better way to get this, The village doesn't have much in stock, and I burn through most of it trying to get the formula right), two parts snow(Full blocks, not balls or sheets), and the standard simple catalyst. 
aw.entries.alchemy.chemistrySet.7=Terrae: A hard, grainy powder that has the power of the earth. Composed of two parts obsidian, one part dirt, one part sand, and mixed together in a simple catalyst.
aw.entries.alchemy.chemistrySet.8=Aquasalus: A reagent that seems to shift between powder and liquid on a whim, it represents the force of water in alchemy. Made by mixing three parts water(the amount in water bottles seems perfect), a ink sack(I didn't ask Magus why he has a huge stock pile of these in his storeroom, and he doesn't seem the type to mention it on a whim), and a simple catalyst.
aw.entries.alchemy.chemistrySet.9=Tennebrae: If sanctus is solid light, tennebrae is hardened shadow. Black as night and seems to dim everything around it, this reagent has potential to be powerful is used correctly. Made with obsidian, two pieces of coal, a ball of clay, and a simple catalyst.
aw.entries.alchemy.chemistrySet.10=Magicales: Raw alchemical power seems to pulse in this dust, and I feel like I have only glimpsed the true potential of this reagent, and I both fear and adore it. Made by mixing one part redstone, one part gunpowder, and two parts glowstone in a simple catalyst.
aw.entries.alchemy.incense.1=>A note, this is an entry written several years after the others before and after this. After talking to the others, I am adding this here as a post script to aid others who read this for the simple reason of "We would have loved to have this when we were starting out". This will, however, mention people and items that might be foreign to you for now. Roll with it, it will all make sense later.< 
aw.entries.alchemy.incense.2=I have found something... Well, unexpected to say the least. The self proclaimed hedge mage Athis stopped by our still in construction homestead in Venric's folly to visit Tiberious. Apparently in one of the letters he wrote her he mentioned being unable to relax after working on watching the demons running around and building various things he and Magus decide we need here(Isn't this meant to be a temporary base of operations?). Well, she came by with a small pouch of wood ash incense and a little brazier to help him with this, but what this provided was far more valuable than simple scented air. I joined Tiberious and Athis for the first use of the incense, and to her credit I started feeling relaxed breathing in the sweet scents it let off. Part way through I got up to visit the blood altar, being my turn in the roster to top out out communal soul network. After performing a sacrifice like normal, something odd happened: There was more LP in the altar then there should be, by a fair margin. Several sacrifices later, I couldn't get it to happen again, so I went in to mention it to Tiberious(While it might have been a simple miscalculation, we must always look deeper into it. This philosophy was passed down to us by Magus, and has saved us time, lead us to great discoveries like this, and at times has resulted in a lot of cheese.). He performed his own sacrifice in the altar, and again there was more LP then there should be. Long story, involving investigations into the local flora, positions of stars, and phase of the moon, short we discovered it was the incense of all things causing the discrepancy. As it turns out, being under the effects of this simple wood ash was resulting in a greater exchange of raw life essence for a given amount of blood, akin to how self sacrifice runes work. After some experimentation, we have a basic calculation for the process and method for the most profit. You must sit in range of the improved brazier I have designed after giving it wood ash until white particles are emitted(You can do the sacrifice before this, but you won't get the full effect). You then must sacrifice almost all of your blood in one quick burst(This is achieved by holding right click for several seconds, then releasing), after this Lp will be added to the altar based on the number of hearts sacrificed, plus the bonus of the self sacrifice runes in the altar, then this total is multiplied by the effect of highest quality incense you are under. An example: If you sacrifice nine hearts into an altar without any self sacrifice runes while under the full effect of wood ash, you would add 2160 LP to the altar instead of the normal 1800. This process isn't without issues, however, as this rapid expenditure of blood weakens you for a time, with an effect not unlike soul fray.
aw.entries.alchemy.incense.3=Now then, I mentioned "Highest quality incense" before. Well, after the revelation that mundane wood ash would offer a sizeable boon to LP creation, I descended into my lab to perfect the process by creating better incense. After three days, ten pots of coffee, and one nap I emerged with four new incenses: Byrrus, Livens, Viridis, and Purpura for bonuses to LP creation of 60%, 120%, 200%, 300% respectively, compared to the 20% of wood ash. There is a minor issue, however: You can't just make and use the strongest one and be done with it. You see, these are designed to be used with each other and wood ash in a sort of chain. Once you reach the peak of wood ash, Byrrus can start taking effect, once you are at it's peak Livens can start, and so on. I should also note that while the LP gains they offer are improved, the relative scents they can let off vary from par with the wood ash to as foul as a compost pile. Sacrifices must be made, and a few foul fumes is worth the gains. I should note that you can speed up the process by which you are effected by increasing the volume of vapours inhaled. In laymans terms, the more braziers active with the same incense around you the faster the effect can take hold.
aw.entries.alchemy.potions=That is it for the reagents, for now. The other use for the alchemical chemistry set I have found is the creation of potions unlike anything anything I have ever seen. While not big on taste(But really, has there ever been a potion that isn't hard to swallow?), the potions it makes are more... refined. Using a single flask(Made in a T2 blood altar with2K and a glass bottle, the first being made by accident when I dropped  the bottle in the lab after tripping on a table leg that had been moved), a potion ingredient, and a binding agent I can turn the solution in the flask(A mixture of nether wart, redstone, and glowstone dust) into a potion that has eight uses or "Swigs" instead of the normal one per bottle. Another interesting power of these new potions is that the red and blue alchemy items I had acquired from the adventures(Who in turn got from some chest in a dungeon) can be used to "Augment" the potions by mixing them in with more of potions ingredient. The red ones I have dubbed "Power catalysts", making the potions effect much more potent at the cost of shortening the effect. The blue ones I have started calling "Lengthening catalysts", as they make the potions last much longer then normal.  On another note, I have started trying to make a better binding catalyst after an idea came to me from a mishap in the lab. I had tried to make a slowness potion(We had a few villagers that had been pestering us, so I wanted to get rid of them without hurting them), but after adding in the sugar powered I tried mixing in a fermented spider eye. Trying to add it in with a weak binding catalyst, I noticed that instead of mixing with the speed potion it was forming it's own section the same color of a weakness potion. Then the flask exploded. If my theory holds true, these potions can hold more then one effect, but the solution destabilizes when an effect is added. Should the potion fully destabilize, it will cause a small explosion that shatters the flask and damages anyone close to it with the glass shards. If I can help keep the potion stable, I might have found a secret of alchemy countless mages have sought but never found....
aw.entries.alchemy.reagentRevolution.1=I have been experimenting with what I can make with this new blood orb, a gift from Tiberius for my help with his sigils. Using this orb I have finally been able to recreate the power and lengthening catalyst. Sadly, I can only make the mundane versions of each, but one step at a time. I haven't actually made many new items for my potions and alchemy(Well, compared to the shift from weak to apprentice orbs) but everything I have found is extremely useful with my potions. Then there is what I've been making for my brother, but I wrote most of the uses of them in his book. I'll add the recipes here anyway, for completeness sake.
aw.entries.alchemy.reagentRevolution.2=The first step towards these new reagents was to make a better form of catalyst. Surprisingly, this was the easy part. By mixing in a netherwart and bonemeal into two simple catalysts, I could create two "Strengthened catalyst" with which to create new reagents and enhance old ones.
aw.entries.alchemy.reagentRevolution.3=Shortly after this discovery, I managed to make a reagent I have dubbed "Offensa" by mixing in two arrows, a flint, and a strengthened catalyst into an incendium. While the original reagents represented elements, these new reagents seem to represent ideas and concepts(With "War and battle" being given to offensa).
aw.entries.alchemy.reagentRevolution.4=More tinkering gave birth to Praesidium, which seems to represent "Defense and protection" and is created by mixing tennibrae into strengthened catalyst, iron ingot(Ground into a powder at the set), redstone dust, and a cobweb(This may or may not have fallen into the set when Bella was doing some cleaning, ending with this happy accident).
aw.entries.alchemy.reagentRevolution.5=By strengthening terrae with the better catalyst, sand, gun powder, and a netherrack chunks(Luckily, we still have some from the last hell harvest festival but I must remember to buy more in a few months), I have created orbis terrae. Where terrae seems to be the element of earth, orbis terrae is the environment itself, encompassing above and below ground from the dessert to the nether.
aw.entries.alchemy.reagentRevolution.6=After a few days of nothing, I managed to form a powder now called "Virtus" from two parts redstone, one part coal, and one part gun powder mixed into a strengthened catalyst. From what I can tell, this is the raw manifestation of power(Fitting, this reagent is hard to control and a few of our uses of it have backfired horribly). It holds in it a great deal of potential energy, but releasing it(And then controlling it) has proven to be very difficult.
aw.entries.alchemy.reagentRevolution.7=This next one is a little odd. Late one night in the lab I had been mixing things together to see the results, and something caused me to mix a gold ingot powdered in the stand, soul sand from our nether wart farm, redstone dust, and a carrot of all things in the strengthened catalyst. When I awoke the next day, I found three things: A reagent called reductus(The aspect of reduce and diminish), a large mess in the lab, and that this wasn't the strangest things I had mixed together. Since then I have started brewing pots of coffee before I begin working late in an attempt to stay lucid.
aw.entries.alchemy.reagentRevolution.8=The final reagent that I have created is called "Potentia", due to the energy that causes it to crackle and pop on its own(Note: As my brother accidentally discovered, this powder does have some electrical properties to it that caused his hair to stand on end when he was working with it). Created with two chunks of lapis lazuli, glowstone dust, a piece of nether quartz, and a strengthened catalyst, this powder is similar to virtus. The difference is that while virtus is raw power, potentia is energy. Ironically, virtus is the more stable of the two, only giving off energy in large bursts while potentia seems to give off a little at all times. 
aw.entries.alchemy.newPotions=After a few weeks worth of work, I finally managed to upgrade the binding agent and have made one that lets me make more advanced potions with more then one effect. Unfortunately, it doesn't work every time. For the first effect it works every time, like the weak binding agent. For the second effect, it only works four times out of ten(On average). Then the third effects works only sixteen times out a hundred(Again, on average.) It also needs 1.5K LP to make the binding agent when mixing a weak binding agent, a sanctus and a crystallos. With how rare some of the ingredients of my potions are, I decided to create a "Filling agent" that replaces the solution in the potion flask so that I can get more uses out of them. I have noticed that potions with more then one effect require stronger filling agents, as every effect in the potion cuts down how effective the agent is. The weak filling agent is made with a simple catalyst, a pile of redstone and glowstone dust, and a piece of nether wart.  The weak filling agent can then be upgraded with a terrae and 1K LP into a "Standard" filling agent.
aw.entries.alchemy.soulSand=I managed to find a new use for the weak blood shards that the others have been gathering. Since the shards are technically part of a "Soul", I tried mixing it in with a bucket of water and sand using my chemistry set to get a fairly close replication of an uncommon nether resource, soul sand. While not identical, it can serve the same purpose as the normal one found in the nether.
aw.entries.alchemy.timeGoesBy=It just occurred to me, it has been over three years since I came to Magus's doorstep with my brother and sister. I've gotten so use to living here, and the daily routine seems like something I've done all my life. Strange, I never thought I'd settle down. Five years ago, I'd have never considered leaving the caravan. I have barely left my lab in the last month, aside from when Magus, my brother or sister drag me out by  the back of my robe. Normally I would go out willingly, but we recently finished our altar upgrade and have created the "Master Blood orbs". As is customary, this new orb has given me the boost in power needed to create more alchemy items for both general use and my potions. As a result, progress has been coming in leaps and bounds with something new almost every other day. After many late nights and early mornings, theses are the fruits of my labor.
aw.entries.alchemy.catalysts=I managed to improve the catalyst even further by mixing in a gold nugget and the fragments of a bone into the strengthened catalyst, making it into a much more concentrated form, hence the name "Concentrated catalyst". With it, I have been able to infuse the imbued slates created in the blood altar with alchemical power, making it stronger then normal and able to construct devices to hold alchemical and magical energy.  On the potion items, I can finally make all the forms of the power and lengthening catalysts, filling agents. By merging two of a catalyst with a standard binding agent you can make the standard form of said catalyst, and then you can create the greater form of the catalyst by fusing two of the standard with a reagent(Incendium for power and Aquasalus for lengthening). Adding these to a potion is the same process as the mundane, place the potion and the ingredient in the stand with the catalyst of choice. Note: You don't need to teir up from normal to the greatest, you can jump strait to the greatest effect by mixing in a greater catalyst. Adding a better catalyst to an old potion does nothing for the effect, and is the same as if you added it to a potion that never had a catalyst added. The greater filling agent is able to resort six charges to a one effect potion, and is made by adding aquasalus and magicalus to a standard filling agent. The remainder of my time has been spent brewing potions. Bella has almost finished the demon summoning area, and I have been creating a few reagents for that and brewing potions to buff myself during the fights. Bella has mentioned a beast called an "Elemental", and it has me slightly worried. A few new bruises later, and I now have a demon blood shard to call my own. I also have one procured by Magus to improve his ritual activation crystal with, and I will need to use a few reagents to merge the blood shard with the crystal, and some experimentation will be needed to find out what...
aw.entries.alchemy.activationCrystal=A few tries later, and the binding of the demonic power to the crystal works with Aquasalus, Incendium, and Aether. In theory a nether star can be used in place of a demon blood shard, but considering how hard those are to get a hold of... Magus has been thrilled with his new crystal, and I have started work on a new project with Tiberius as a surprise for Magus. His rituals as they stand are good, but I have had an idea to make them even better... I'm having a hard time sleeping tonight, so I might as well go over our idea and hope Magus doesn't read this. As it stands, the rituals only use life essence to run and are often "One trick ponies". What I proposed to Tiberius is this: What would happen if one of the rituals was charged with alchemical power, costing reagent to give it new or stronger effects? We might get nothing, or something amazing. Time will tell which it is.
aw.entries.alchemy.reagentSystem.1=It has taken the last two months, but we have the ground work for testing our idea done. The results so far have been even better then expected. We only have tested the full spring ritual and have discovered quite a few effects, but I'll let Magus cover those. 
aw.entries.alchemy.reagentSystem.2=Instead, I'll write how the reagent system works. You first need an alchamic calcinator, then place a blood orb into it to act as a power source. After that, reagents can be placed by hand or fed into the calcinator to turn into pure alchemical energy, the type based on the reagent fed in. Once it has a solid reagent, it will use LP to turn one reagent into 1000AR(Alchemy Reagent) which is then moved into the 2000AR "Buffer" of the calcinator. Once in the buffer, the reagent slowly starts being moved into the main tank which can then be siphoned out.
aw.entries.alchemy.reagentSystem.3=Now then, to move it out of the main tank you need to have somewhere for it to go. To this extent, you have three options: A crystal belljar, a alchemic relay, or a master ritual stone. How you need to link it is simple: Create a alchemic router, then Shift right click with it on the calcinator to select the reagent type for it to transport(A note for blocks with more then one tank and more then one reagent type: Shift right clicking again on the block will swap between the available reagents inside the block.), then normal right click on the block with the router to bind it to the block. After that, right click on the destination(Needs to be within a few blocks, the range isn't that large). If it has worked, you should see reagent "Flowing" to the destination from the source(In this case the calcinator). We have tweaked it to where you can't see reagent moving with the naked eye, you need to have a tool(Like the Alchemic router) in hand to see this effect. You can also use a divination sigil on the belljars, relays, calcinator, and master ritual stone to read how much reagent the device holds. This process of binding can be applied to all the other blocks so I shouldn't need to cover it further, but I should note that these blocks can only accept and send reagent to so many locations at once. For example, the belljar can only accept reagent from one location, and send it to one location. I must also reiterate that if you have a relay set to send aether to destination A and Tennibrae to destination B, aether can never traval to B and vicversa for Tennibrea. New connections must be made for each reagent type, and we could not get a "Colorless" version of a connection to function.
aw.entries.alchemy.reagentSystem.4=Getting more into detail about the blocks and items of our new system, I'll cover the bell jar first. Created out of mundane glass and wood slaps, then filled with a concentrated catalyst solution to contain the alchemical energy in a stable enough environment to allow you to move the jar without losing any energy inside. While it can hold only one type of reagent and is restricted to one source and one destination, it functions as a portable form of "Bulk storage"(Able to hold 16000AR). An option for more storage is to link a few belljars together, but I feel this is a far from perfect answer to the problem of mass storage of reagent.... I will need to talk with Tiberius and Magus about this later. Regardless, Tiberius managed to incorporate a bit of redstone interaction into the design of the jar. Based on the amount of reagent inside the jar, it outputs a redstone signal(15 when full, 0 when empty, and so on). With a bit of work, you can use this for automated systems.
aw.entries.alchemy.reagentSystem.5=The "Alchemy Relay" acts as a small buffer(Two tanks that can hold two reagent each), and can be used to merge and divide a transport line of reagent energy. Each relay can have limitless sources, and four destinations. With the creative use of relays, you can create a large power network for transporting reagent. Tiberius has worked his magic on this device as well, and tweaked it to not send reagent when a redstone signal is applied(It also spins slightly slower to represent this).
aw.entries.alchemy.reagentSystem.6=To assist with the use of this system, we have three items to manage these devices. The Alchemic Router is used to bind the destinations of set reagents for all devices. The Alchemic Segmenter is used to partition tanks to a set reagent. All you need to do is set the reagent type the same way you would the router, then right click the device you want to partition a tank for that reagent type. One tank will be set for that type per right click, and if you exceed the maximum number of available tanks it will reset all tanks partitioned tanks to the default "First come first served". I should also note that if you set a relay to accept sanctus, offensa and vistus, the two available tanks will distributed to whichever reagents that get there first. The final item is the alchemic cleanser, and its use is simple but very useful: It resets all destinations. All locations the device is set to send reagent are cleared, so you can easily bind the device to new locations.
aw.entries.alchemy.reagentSystem.7=Now, we have made a few minor alterations to the "Master ritual stone" to enable our system to function. Its recipe and normal uses have remained the same, but it now has three internal tanks that each hold 1000AR. If the ritual has any reagent, it will display an "Alchemy Ring" above the master ritual stone as a sign that it has been augmented. Then depending on the ritual and the effect in question, it will use an amount of reagent to perform a new effect or augment an old one. An example of this is the full spring fulled with aquasalus will convert earth into farmland then hydrate it in a nine by nine around the master ritual stone, but if the full spring is given crystallus it will freeze all water source blocks around it. This all is paired with the base effect of the rituals, so the full spring can still function as an endless water source. This is all I can think of to write down about our new system. Tiberius and I plan to show it to the others tomorrow, so I need to get some rest. We showed our new system to Magus and my siblings today, and to say they loved it was an understatement. We had decided to be a little flashy with the show, building a full spring next to a small pond that had formed from the recent rainfall. After a bit of work hiding the relays and the belljar, Tiberius hid out of sight while I showed the others to the demonstration. I asked Magus to activate the ritual, keeping a close eye to see if he focused on anything in particular, but he quickly and effortlessly activated the ritual without pausing for a moment. After he returned to my side, I yelled for Tiberius to throw the switch. In an instant the reagent flowed to the ritual, and as the alchemy circle formed in the air the water created by the ritual and that of the pond quickly froze over. I heard gasps from both of my siblings, and I noticed Magus's eyes widen for just a moment(His equivalent of a jaw hanging slack) before he composed himself. Afterword we went over the details for them of how it works for the other, explaining the minor modifications we had made to the basic master ritual stone design and such.
aw.entries.alchemy.magusSecret=It has been a week since we ironed out most of the kinks with the reagent system, and Magus brought all of us together around the dinner table to go over the next few months. He said that he could get us something he felt would be needed to upgrade the altar to teir five, but it would take a little over a year to get them. He also mentioned that we should be getting a few messages after this years Hell harvest festival, which I thought was a little odd. "A little odd" doesn't begin to describe what has been happening the last few days. It seems most of us have put research on the side while we watch some of the "Replies" come in. They have come in via air drop, mechanized mail carriers, train and countless other ways that always seem stranger then the last. Tiberius managed to ask Magus what this was all about and he simply replied that he invited a few guests over, and these were the replies they had sent. When pressed for more details, he just said they wouldn't be coming for a few months, so we should start mastering what we can do with our blood orbs. We then started showing each other the little details of the branches of blood magic we had created and had been perfecting.  In the last few months I have befriended a few demons with the help of my sister, created a few spell crystals and help construct a large complex spell with my brother, and then forged armor and sigils with Tiberius. Then it came time to teach the other alchemy and potion brewing.... I was reluctant to give out my secrets to brewing potions, even to them. I had told no one of the recipes for these brews, brewing potions for the others myself when they needed them. I almost wanted to tell the others no, but then Magus made me an offer. "I understand how you want to keep the secrets of your brews hidden, so how about we make a trade of knowledge instead?" he asked me when I was contemplating this matter. He offered me details about himself, somethings that wasn't common knowledge and even gave me permission to write everything down. Considering how little I knew of him, even after all these years, I quickly took him up on this offer. The very next day I had everyone in my personal lab, showing then the details and reciting the recipes for some of the most potent effect I had found then demonstrating there uses.  I was done showing them everything shortly before dinner, so while we ate in the common room Magus told us his story. I'll jot as much of it as I can recall here, for you to enjoy as well. "So it is time to tell my story... I always hate doing this. So many questions that I really don't have answers for, and frankly it is a long bloody story. But a deal is a deal, and you all deserve to hear it. I have only told a handful of people this, all of them were either mages or Conglomeration members. You will so count among this number... I was born in the middle of the Talus kingdom, before all these corporations were founded and started forming their own governments. Back then gods and kings dictated law, and wars were fought on the battle field and not in markets shares and advertisement campaigns. My family was small, all I had was  a mother who did all she could at home and a father who worked at the royal library. I ended up spending many hours in that library, both helping my father with his work and studying the countless tomes that adorned those shelves. At an early age I discovered a hidden treasure trove in those shelves: Books on magic. At the time magic wasn't well known, and most regarded it as nothing but myths created by fools. Because of this, theses books stayed lost and forgotten on these shelves until I came along. While other boys were learning the right way to hold a sword or a pickaxe, I spent my time studying the arcane. At the end of my first year there, I had already created a basic fireball spell and a thaumanonicon. I managed to keep my activates in magic hidden from everyone, my parents include, until I had come of age and needed to find a profession. It was then that I told them of what I had been working on all those years, showing them some of the smaller tricks I had perfected.  To say they were shocked is an understatement, so I felt it best to leave their little world in the kingdom and set out to find a new home to practice magic without needing to be as secretive. That is how I came to Lurric, and it has been my home ever since then. The early days were hard, as I really didn't have the funds to live off of let alone support my research. I managed to get by helping the locals, merchants with book keeping and identifying some of the more obscure relics and "Treasures" they had for sale, farmers with stock and management, and even as a cook when times were really dark for me. But this only lasted a year or two, then I had the infrastructure in place to support myself with a little extra to live on. Amazing how much traders are willing to spend floating balls of flame and purple metal.... Skipping ahead a few years through the dull portion, I had begun to master a few forms of magic like thaumurgy and botany. My name had started to spread to the other nearby villages, and people asking for favors and knowledge started popping up at my door like they do now. At the time I never asked for gifts or compensation, but I didn't turn down food or books if they were offered. Traders started traveling to Lurric more and more, hoping to acquire a few goods that only I could provide at the time in exchange for gold, jewels, and rare books. Those who brought the third I gave the best deals. Still, life went on like this for a few more years with books starting to pile up in my library, more then enough gold to pay for my more "Exotic" materials, and never having a day of rest for the full decade I had first lived here. That peace was shattered the day a man by the name of Gareth broke down my door.  He came in, adorned in full iron armor and long sword in hand, to challenge me to a dual to the death. It took more then a few sleep spells to knock him out of his rage, at which point I restrained him to a reclined chair I had in the back that I used when treating wounded. When he finally awoke, I started interrogating him. Well, interrogate isn't quite the right word for it.... Honestly, I just asked him a few questions. Once I saw that he had calmed down, I untied him and offered food. Did I mention the sounds his stomach made while he was unconscious? They were strong enough to rattled the chair. After eating a whole bread loaf and a large part of the stew I had been preparing before he arrived, we sat in this very room to talk like civil beings over why he decided to break down my door. His story is about as ludicrous as mine is. He was one of fifty seven immortal beings who walked the earth. He told me what details about he could, but he seemed unsure about it as well. What he did tell me was that the gift of immortality comes with a few boons aside from the obvious, and what they are differ from person to person. He said his gift was that of prophecy, and he recently had a vision of the future where I and people I influence would play a major role. As a result, he came here to challenge me to a dual to the death. I then asked him if that wasn't a bit unfair, him being immortal and all. He, in the most serious tone possible, said that it was completely fair: While an immortal can not be killed normally, there are set way that it can happen. One way is by a one on one dual to the death, with immortality wagered as the prize. They really don't have much choice in this, as it is like natural magic: The mere act of it the dual happening is enough for the "Wager" to take place. If both parties exert their full strength, then the killing blow is enough to complete the wager and transfer the gift.  We talked for the next few hours, and I learned quite a few interesting facts. He had no idea who the other fifty six immortals were, and he frankly had no way to tell them apart from normal humans. He also mentioned that he could still be injured outside of the dual, but he would not fall to mortal wounds and heal at an accelerated rate. Not much of one however, as he mentioned breaking a leg once and it taking over a month to fully heal. Another note was how short a time he had been immortal, he had gained it about two decades ago. While to you that seems like a long time, he said the one he killed was alive for over a century. We finally ended that night with an agreement: In one weeks time he would return to my home, where we would share a fine meal, then have our dual the next morning. He wanted to get strait to the dual, but I had the meal as a non-negotiable point that he finally caved on.  I spent the next week preparing for the battle, arming myself with the most potent spells and artifacts at my disposal from several schools of magic. In a flash that week went by, then Gareth once again sat with me to eat at my table. I gave him a bed to sleep in that night, then at dawn we both walked to the hill just behind this house where Bella's garden currently lays. Of all things about Gareth,  our battle was one things I barely remember. Once we reached the top of the hill, we both brew our blades and turned to each other. After that, I only remember flashes and out of order scenes. It felt like it was over in only a few minutes, but it might have been hours and was one of the most brutal fights I have ever been a part of .  His agility was far greater then I expected, dodging my swings and spells with ease. Unfortunately for him I was still a force to be reckoned with back then. I never let up on my spells, casting fire, ice and lightning one after another using my foci then teleporting around with a few spells. In the end, he died when he dropped his sword in reflex to the heat my flame spell had left on it and I capitalized on this. By this point we both felt taxed by the fight, and he could barely avoid my slowest attacks. The final blow I dealt was out of mercy, a quick end for his suffering that I had caused. I watched as Gareth grinned at me like he knew this would be how he died, then he closed his eyes as his body turned into sand carried away in the wind. I feel to my side after that, strength sapped and mana depleted, to awake a few hours later. I felt a new strength in my body when I got up, slight but still there. All in all, suddenly having immortality was rather lack-luster, but that was how it happened." At this point I was shocked into silence at his words. Not what he said, but how he said them: Little feeling or emphasis. It was like he just said that he gained a new scar or it started to rain. He has been more emotional over the acquisition of a rare book then immortality. Glancing at the others, I could tell they were mulling over this information as well. After a few minutes, my brother spoke up to start the questions. "So... Did your life change much after that?" Demir asked Magus, before taking a bite out of a sandwich he seem to have created from thin air. "Not really. Life as an immortal was just the same as life as a mortal. More researching, more learning, more magic.".  "Have you met any more immortals since then?" Bella asked. "Two, and I happen to be related to both of them." "Related?" asked Tiberius with curiosity, "I never knew you had any relatives." "Well, they never came up. One of them is my brother, who started studying the Myst a few decades ago and hasn't been heard from since. I expect he will pop by any time he feels homesick for a few days, then drop out of touch again." "And the other immortal?" Bella asked with interest. "The other is my wife." Once again we all went into stunned silence, Tiberius and Demir slack-jawed. "You're married! Since when?" Bella asked after a moment. "Few centuries ago. We met when a small war was being fought with a necromancer, and she was the one leading the forces. Of course, most people at the time didn't know her real gender. She tried hard to keep it hidden, so the troops wouldn't think of her any differently. I was brought in to help deal with the "Undead army" details of the whole thing, and that had me working with her. One thing lead to another, and we were married a few months after the war was over. Currently she works as a commander of the Conglomeration's defense force, so she is rarely has time to come home."  The questions continued for the next half hour, ranging from "Did you really open the first nether portals" to "Why are you on the Conglomeration board?". Magus took all are questions with the air of a man who has done this all before. When the questions were over, all the others started retiring to their rooms while I stopped by the library to look up something. The Talus kingdom fell almost a millennium ago. Wow... It has been a few months since I last wrote in this book. I've met more people in the last few weeks then I have in the last few years, many of them from lands I have never set foot in! Kings and queens, rulers and underlords, CEO's and owners from every nation. Then there was the fight with the withers... I'm not sure what to say about all that. I sort of walked in there, chugged a few potions then let the years of training with Magus take over. By the time the adrenalin rush was over for me, we had killed all of the withers and I was barely aware of a dull pain in my leg. Magus has used a spell to restrict my movement so I won't leave this bed while my broken leg heals since I "May" have tried to sneak into my lab once or twice to get to work. For now I must be content with my notebook and what Magus brings me from his library to past the days...
aw.entries.alchemy.simpleCreations=I've only had a little time at my chemistry set, so I haven't had much time to experiment with my new orb. All my tests trying to make new reagents failed, and I so far have only managed to make two new runic slates for my brother(But we have been trying to find new uses for them). The normal runic slate is a simple upgrade to the old cracked slates by infusing terrae into them. The final slate, dubbed imbued, is two runic slates bound together with magicales, incendium, and aquasalus.  These days I've been working with the other on a few secret projects, so I have needed to keep up with and research all kinds of strange subjects. I also always seem to need to make more reagents, as our experiments burn through them rather quickly... I will write more when I have the time.

guide.BloodMagic.entryName.architect.intro=A Classic Tragic Back-story
guide.BloodMagic.entryName.architect.bloodAltar=The Blood Altar
guide.BloodMagic.entryName.architect.soulNetwork=The Soul Network
guide.BloodMagic.entryName.architect.blankSlate=Basics of Sigils
guide.BloodMagic.entryName.architect.divination=The Divination Sigil
guide.BloodMagic.entryName.architect.waterSigil=Training, and a Water Sigil
guide.BloodMagic.entryName.architect.lavaCrystal=The Lava Crystal
guide.BloodMagic.entryName.architect.hellHarvest=Hell's Harvest Festival
guide.BloodMagic.entryName.architect.lavaSigil=The Lava Sigil
guide.BloodMagic.entryName.architect.blankRunes=Blank Runes - the Step to Tier 2
guide.BloodMagic.entryName.architect.speedRunes=Speed Runes
guide.BloodMagic.entryName.architect.apprenticeOrb=The Apprentice Blood Orb
guide.BloodMagic.entryName.architect.voidSigil=The Void Sigil
guide.BloodMagic.entryName.architect.airSigil=Air Sigil
guide.BloodMagic.entryName.architect.sightSigil=Sigil of Sight
guide.BloodMagic.entryName.architect.advancedAltar=Advanced Altar Mechanics
guide.BloodMagic.entryName.architect.fastMiner=Sigil of the Fast Miner
guide.BloodMagic.entryName.architect.soulFray=Soul Fray
guide.BloodMagic.entryName.architect.greenGrove=Sigil of the Green Grove
guide.BloodMagic.entryName.architect.dagger=Dagger of Sacrifice
guide.BloodMagic.entryName.architect.sacrifice=Runes of Sacrifice
guide.BloodMagic.entryName.architect.bloodPack=The Blood Letter's Pack
guide.BloodMagic.entryName.architect.fivePeople=And Then there was Five
guide.BloodMagic.entryName.architect.tier3=The Next Step: Tier 3
guide.BloodMagic.entryName.architect.magicianOrb=The Magician's Blood Orb
guide.BloodMagic.entryName.architect.newRune=New Runes
guide.BloodMagic.entryName.architect.magnetism=Sigil of Magnetism
guide.BloodMagic.entryName.architect.phantomBridge=The Phantom Bridge
guide.BloodMagic.entryName.architect.holding=Sigil of Holding
guide.BloodMagic.entryName.architect.elementalAffinity=Sigil of Elemental Affinity
guide.BloodMagic.entryName.architect.ritualStones=Recreating Ritual Stones
guide.BloodMagic.entryName.architect.bloodLamp=Shining a Blood Lamp
guide.BloodMagic.entryName.architect.boundArmour=Bound Armour
guide.BloodMagic.entryName.architect.sanguineArmour=Sanguine Robes
guide.BloodMagic.entryName.architect.soulSuppress=Suppressing the Soul
guide.BloodMagic.entryName.architect.ritualDiviner=The Ritual Diviner
guide.BloodMagic.entryName.architect.bloodShard=Blood Shards
guide.BloodMagic.entryName.architect.tier4Altar=The Life of a Mage - Tier 4
guide.BloodMagic.entryName.architect.masterOrb=The Masters of the Tier 4 Altar
guide.BloodMagic.entryName.architect.whirlwind=Sigil of the Whirlwind
guide.BloodMagic.entryName.architect.compression=Sigil of Compression
guide.BloodMagic.entryName.architect.severance=Sigil of Ender Severance
guide.BloodMagic.entryName.architect.teleposer=The Teleposer
guide.BloodMagic.entryName.architect.suppression=Sigil of Suppression
guide.BloodMagic.entryName.architect.superiorCapacity=Rune of Superior Capacity
guide.BloodMagic.entryName.architect.orbRune=Rune of the Orb
guide.BloodMagic.entryName.architect.fieldTrip=A Field Trip
guide.BloodMagic.entryName.architect.bindingKey=The Key of Binding
guide.BloodMagic.entryName.architect.tier5Altar=The Trials of a Tier 5 Altar
guide.BloodMagic.entryName.architect.priceOfPower=The Price of Power
guide.BloodMagic.entryName.architect.demonicOrb=The Archmage's Orb
guide.BloodMagic.entryName.architect.energyBazooka=Power of the Energy Bazooka
guide.BloodMagic.entryName.architect.accelerationRune=Acceleration Runes
guide.BloodMagic.entryName.architect.harvest=The Harvest Godess
guide.BloodMagic.entryName.architect.demonProblem=Solving a Demon Problem
guide.BloodMagic.entryName.architect.tier6Altar=Tier 6 Already
guide.BloodMagic.entryName.architect.moreThanHuman=More Than Human: Project Omega

guide.BloodMagic.entryName.rituals.intro=Introduction
guide.BloodMagic.entryName.rituals.weakRitual=Weak Rituals
guide.BloodMagic.entryName.rituals.rituals=Rituals
guide.BloodMagic.entryName.rituals.waterRitual=Ritual of the Full Spring
guide.BloodMagic.entryName.rituals.lavaRitual=Serenade of the Nether
guide.BloodMagic.entryName.rituals.groveRitual=Ritual of the Green Grove
guide.BloodMagic.entryName.rituals.interdictionRitual=Ritual of Interdiction
guide.BloodMagic.entryName.rituals.containmentRitual=Ritual of Containment
guide.BloodMagic.entryName.rituals.bindingRitual=Ritual of Binding
guide.BloodMagic.entryName.rituals.beastMode=Beast Mode
guide.BloodMagic.entryName.rituals.unbindingRitual=Ritual of Unbinding
guide.BloodMagic.entryName.rituals.jumpRitual=Ritual of the High Jump
guide.BloodMagic.entryName.rituals.duskInk=Dusk Ink
guide.BloodMagic.entryName.rituals.magnetismRitual=Ritual of Magnetism
guide.BloodMagic.entryName.rituals.crusherRitual=Ritual of the Crusher
guide.BloodMagic.entryName.rituals.speedRitual=Ritual of Speed
guide.BloodMagic.entryName.rituals.shepherdRitual=Ritual of the Shepherd
guide.BloodMagic.entryName.rituals.darkMagic=Dark Side of Magic
guide.BloodMagic.entryName.rituals.knifeAndSufferingRitual=Feathered Knife and Well of Suffering
guide.BloodMagic.entryName.rituals.regenerationRitual=Ritual of Regeneration
guide.BloodMagic.entryName.rituals.harvestFestival=The Hell's Harvest Festival
guide.BloodMagic.entryName.rituals.thenThereWereFive=And Then there were Five
guide.BloodMagic.entryName.rituals.alchemyRitual=The Ballad of Alchemy
guide.BloodMagic.entryName.rituals.domeRitual=Dome of Suppression
guide.BloodMagic.entryName.rituals.awakenedCrystal=Awakened Activation Crystal
guide.BloodMagic.entryName.rituals.featheredEarthRitual=Ritual of the Feathered Earth
guide.BloodMagic.entryName.rituals.gaiaRitual=Gaia's Transformation
guide.BloodMagic.entryName.rituals.condorRitual=Reverence of the Condor
guide.BloodMagic.entryName.rituals.meteorRitual=Mark of the Fallen Tower
guide.BloodMagic.entryName.rituals.expulsionRitual=Aura of Expulsion
guide.BloodMagic.entryName.rituals.costOfProgress=The Cost of Progress
guide.BloodMagic.entryName.rituals.zephyrRitual=Call of the Zephyr
guide.BloodMagic.entryName.rituals.harvestRitual=Reap of the Harvest Moon
guide.BloodMagic.entryName.rituals.eternalSoulRitual=Cry of the Eternal Soul
guide.BloodMagic.entryName.rituals.ellipsoidRitual=Focus of the Ellipsoid
guide.BloodMagic.entryName.rituals.evaporationRitual=Song of Evaporation
guide.BloodMagic.entryName.rituals.sacrosanctityRitual=Ward of Sacrosanctity
guide.BloodMagic.entryName.rituals.evilRitual=Veil of Evil
guide.BloodMagic.entryName.rituals.stomachRitual=Requiem of the Satiated Stomach
guide.BloodMagic.entryName.rituals.reagentEffects=The Effects of Reagents
guide.BloodMagic.entryName.rituals.conclaveOfMages=The Conclave of Mages
guide.BloodMagic.entryName.rituals.forbiddenParadise=Forbidden Paradise
guide.BloodMagic.entryName.rituals.convocationRitual=Convocation of the Damned
guide.BloodMagic.entryName.rituals.longHaul=The Long Haul
guide.BloodMagic.entryName.rituals.phantomHandsRitual=Orchestra of the Phantom Hands
guide.BloodMagic.entryName.rituals.anvilRitual=Rhythm of the Beating Anvil
guide.BloodMagic.entryName.rituals.dawnInk=Dawn Ink
guide.BloodMagic.entryName.rituals.symmetryRitual=Symmetry of the Omega
guide.BloodMagic.entryName.rituals.stallingRitual=Duet of the Fused Souls
guide.BloodMagic.entryName.rituals.newMoonRitual=Blood of the New Moon

guide.BloodMagic.entryName.demons.ashes=The Girl From the Ashes
guide.BloodMagic.entryName.demons.tamedDemon=The First Tamed Demon
guide.BloodMagic.entryName.demons.future=A Glimps into the Future
guide.BloodMagic.entryName.demons.knight=A Wandering Knight
guide.BloodMagic.entryName.demons.demonShard=Pale Blue Demonic Shard
guide.BloodMagic.entryName.demons.demonSummoning=Demon Summoning and Taming
guide.BloodMagic.entryName.demons.keysGate=The Keys to the Gate
guide.BloodMagic.entryName.demons.futurePlans=Future Plans
guide.BloodMagic.entryName.demons.demonInvasion=The Demon Invasion
guide.BloodMagic.entryName.demons.observations=Observations of the Demons

guide.BloodMagic.entryName.spells.demonGirl=Wandering and Demon Girls
guide.BloodMagic.entryName.spells.spellTable=Spell Table Top Games
guide.BloodMagic.entryName.spells.simpleEffects=Spell Effects for Beginners
guide.BloodMagic.entryName.spells.tableAndSkulls=To Make a Table and Skulls
guide.BloodMagic.entryName.spells.timePasses=And so Time Passes
guide.BloodMagic.entryName.spells.complexSpellBasics=Complex Spells, the Basics
guide.BloodMagic.entryName.spells.crafting=Vlad's Guide to Crafting
guide.BloodMagic.entryName.spells.complexSpellEffects=Effects of Complex Spells
guide.BloodMagic.entryName.spells.offTopic=Off topic, This is my Life
guide.BloodMagic.entryName.spells.demonicPower=Demonic Power Unleashed

guide.BloodMagic.entryName.alchemy.fatedMeeting=A Fated Meeting
guide.BloodMagic.entryName.alchemy.firstSteps=First Steps of Alchemy
guide.BloodMagic.entryName.alchemy.chemistrySet=Uses of the Chemistry Set
guide.BloodMagic.entryName.alchemy.incense=Incense Inside
guide.BloodMagic.entryName.alchemy.potions=The Power of Potions
guide.BloodMagic.entryName.alchemy.reagentRevolution=Reagent Revolution
guide.BloodMagic.entryName.alchemy.newPotions=New Potion Improvements
guide.BloodMagic.entryName.alchemy.soulSand=Soul Sand
guide.BloodMagic.entryName.alchemy.timeGoesBy=As Time goes By
guide.BloodMagic.entryName.alchemy.catalysts=Upgrading the Catalysts
guide.BloodMagic.entryName.alchemy.activationCrystal=Activation Crystal Upgrade
guide.BloodMagic.entryName.alchemy.reagentSystem=The Reagent System
guide.BloodMagic.entryName.alchemy.magusSecret=Magus's Secret
guide.BloodMagic.entryName.alchemy.simpleCreations=Simple-ish Creations

guide.BloodMagic.category.architect=The Architect
guide.BloodMagic.category.rituals=The Ritual Master
guide.BloodMagic.category.demons=The Demon Kin
guide.BloodMagic.category.spells=The Battle Mage
guide.BloodMagic.category.alchemy=The Alchemist

guide.BloodMagic.welcomeMessage=
guide.BloodMagic.book.title=Sanguine Scientiem
guide.BloodMagic.book.name=Sanguine Scientiem
guide.BloodMagic.authorName= 

