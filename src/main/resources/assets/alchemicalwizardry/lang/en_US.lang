#Block Localization
tile.bloodAltar.name=Blood Altar
tile.bloodRune.blank.name=Blood Rune
tile.bloodRune.fill.name=Rune of Augmented Capacity
tile.bloodRune.empty.name=Rune of Dislocation
tile.bloodRune.orb.name=Rune of the Orb
tile.bloodRune.betterCapacity.name=Rune of Superior Capacity
tile.bloodRune.acceleration.name=Rune of Acceleration
tile.speedRune.name=Speed Rune
tile.efficiencyRune.name=Efficiency Rune
tile.runeOfSacrifice.name=Rune of Sacrifice
tile.runeOfSelfSacrifice.name=Rune of Self-Sacrifice
tile.ritualStone.name=Ritual Stone
tile.blockMasterStone.name=Master Ritual Stone
tile.bloodSocket.name=Filled Socket
tile.imperfectRitualStone.name=Imperfect Ritual Stone
tile.armourForge.name=Soul Armour Forge
tile.emptySocket.name=Empty Socket
tile.bloodStoneBrick.name=Bloodstone Brick
tile.largeBloodStoneBrick.name=Large Bloodstone Brick
tile.blockWritingTable.name=Alchemic Chemistry Set
tile.blockHomHeart.name=Spell Table
tile.bloodPedestal.name=Arcane Pedestal
tile.bloodPlinth.name=Arcane Plinth
tile.bloodTeleposer.name=Teleposer
tile.blockConduit.name=Spell Conduit
tile.blockSpellParadigm.projectile.name=Particle Generator
tile.blockSpellParadigm.self.name=Self Augmentator
tile.blockSpellParadigm.melee.name=Melee Aggregator
tile.blockSpellParadigm.tool.name=Tool Forger
tile.blockSpellEnhancement.power1.name=Unstable Spell Empowerer
tile.blockSpellEnhancement.power2.name=Standard Spell Empowerer
tile.blockSpellEnhancement.power3.name=Reinforced Spell Empowerer
tile.blockSpellEnhancement.power4.name=Imbued Spell Empowerer
tile.blockSpellEnhancement.power5.name=Demonic Spell Empowerer
tile.blockSpellEnhancement.cost1.name=Unstable Spell Dampener
tile.blockSpellEnhancement.cost2.name=Standard Spell Dampener
tile.blockSpellEnhancement.cost3.name=Reinforced Spell Dampener
tile.blockSpellEnhancement.cost4.name=Imbued Spell Dampener
tile.blockSpellEnhancement.cost5.name=Demonic Spell Dampener
tile.blockSpellEnhancement.potency1.name=Unstable Spell Augmentor
tile.blockSpellEnhancement.potency2.name=Standard Spell Augmentor
tile.blockSpellEnhancement.potency3.name=Reinforced Spell Augmentor
tile.blockSpellEnhancement.potency4.name=Imbued Spell Augmentor
tile.blockSpellEnhancement.potency5.name=Demonic Spell Augmentor
tile.blockSpellModifier.default.name=Default Spell Modifier
tile.blockSpellModifier.offensive.name=Offensive Spell Modifier
tile.blockSpellModifier.defensive.name=Defensive Spell Modifier
tile.blockSpellModifier.environmental.name=Environmental Spell Modifier
tile.blockSpellEffect.fire.name=Crucible of Fire
tile.blockSpellEffect.ice.name=Ice Maker
tile.blockSpellEffect.wind.name=Wind Generator
tile.blockSpellEffect.earth.name=Earth Former
tile.alchemicCalcinator.name=Alchemic Calcinator
tile.crystalBelljar.name=Crystal Belljar
tile.blockReagentConduit.name=Alchemy Relay
tile.lifeEssenceFluidBlock.name=Life Essence
tile.crystalBlock.fullCrystal.name=Crystal Cluster
tile.crystalBlock.crystalBrick.name=Crystal Cluster Brick
tile.demonPortal.name=Demon Portal
tile.demonChest.name=Demon's Chest
tile.enchantmentGlyph.enchantability.name=Glyph of the Adept Enchanter
tile.enchantmentGlyph.enchantmentLevel.name=Glyph of Arcane Potential
tile.stabilityGlyph.stability1.name=Glyph of Rigid Stability
tile.schematicSaver.name=Schematic Saver
tile.blockMimic.name=Mimic Block
tile.blockSpectralContainer.name=Spectral Container
tile.blockBloodLightSource.name=Blood Light
tile.spectralBlock.name=Spectral Block
tile.blockCrucible.name=Incense Crucible

#Item Localization
item.weakBloodOrb.name=Weak Blood Orb
item.apprenticeBloodOrb.name=Apprentice Blood Orb
item.magicianBloodOrb.name=Magician's Blood Orb
item.masterBloodOrb.name=Master Blood Orb
item.archmageBloodOrb.name=Archmage's Blood Orb
item.energyBlast.name=Energy Blaster
item.energySword.name=Bound Blade
item.lavaCrystal.name=Lava Crystal
item.waterSigil.name=Water Sigil
item.lavaSigil.name=Lava Sigil
item.voidSigil.name=Void Sigil
item.blankSlate.name=Blank Slate
item.reinforcedSlate.name=Reinforced Slate
item.sacrificialDagger.name=Sacrificial Knife
item.daggerOfSacrifice.name=Dagger of Sacrifice
item.airSigil.name=Air Sigil
item.sigilOfTheFastMiner.name=Sigil of the Fast Miner
item.sigilOfElementalAffinity.name=Sigil of Elemental Affinity
item.sigilOfHaste.name=Sigil of Haste
item.sigilOfHolding.name=Sigil of Holding
item.divinationSigil.name=Divination Sigil
item.waterScribeTool.name=Elemental Inscription Tool: Water
item.fireScribeTool.name=Elemental Inscription Tool: Fire
item.earthScribeTool.name=Elemental Inscription Tool: Earth
item.airScribeTool.name=Elemental Inscription Tool: Air
item.duskScribeTool.name=Elemental Inscription Tool: Dusk
item.activationCrystalWeak.name=Weak Activation Crystal
item.activationCrystalAwakened.name=Awakened Activation Crystal
item.activationCrystalCreative.name=Creative Activation Crystal
item.boundPickaxe.name=Bound Pickaxe
item.boundAxe.name=Bound Axe
item.boundShovel.name=Bound Shovel
item.boundHelmet.name=Bound Helmet
item.boundPlate.name=Bound Plate
item.boundLeggings.name=Bound Leggings
item.boundBoots.name=Bound Boots
item.weakBloodShard.name=Weak Blood Shard
item.growthSigil.name=Sigil of the Green Grove
item.blankSpell.name=Unbound Crystal
item.alchemyFlask.name=Potion Flask
item.standardBindingAgent.name=Standard Binding Agent
item.mundanePowerCatalyst.name=Mundane Power Catalyst
item.averagePowerCatalyst.name=Average Power Catalyst
item.greaterPowerCatalyst.name=Greater Power Catalyst
item.mundaneLengtheningCatalyst.name=Mundane Lengthening Catalyst
item.averageLengtheningCatalyst.name=Average Lengthening Catalyst
item.greaterLengtheningCatalyst.name=Greater Lengthening Catalyst
item.incendium.name=Incendium
item.magicales.name=Magicales
item.sanctus.name=Sanctus
item.aether.name=Aether
item.simpleCatalyst.name=Simple Catalyst
item.crepitous.name=Crepitous
item.crystallos.name=Crystallos
item.terrae.name=Terrae
item.aquasalus.name=Aquasalus
item.tennebrae.name=Tenebrae
item.demonBloodShard.name=Demon Blood Shard
item.sigilOfWind.name=Sigil of the Whirlwind
item.telepositionFocus.name=Teleposition Focus
item.enhancedTelepositionFocus.name=Enhanced Teleposition Focus
item.reinforcedTelepositionFocus.name=Reinforced Teleposition Focus
item.demonicTelepositionFocus.name=Demonic Teleposition Focus
item.imbuedSlate.name=Imbued Slate
item.demonicSlate.name=Demonic Slate
item.sigilOfTheBridge.name=Sigil of the Phantom Bridge
item.armourInhibitor.name=Armour Inhibitor
item.cheatyItem.name=Orb of Testing
item.weakFillingAgent.name=Weak Filling Agent
item.standardFillingAgent.name=Standard Filling Agent
item.enhancedFillingAgent.name=Enhanced Filling Agent
item.weakBindingAgent.name=Weak Binding Agent
item.ritualDiviner.name=Ritual Diviner
item.ritualDismantler.name=Ritual Dismantler
item.sigilOfMagnetism.name=Sigil of Magnetism
item.itemDiabloKey.name=Key of Binding
item.energyBazooka.name=Energy Bazooka
item.bloodLightSigil.name=Sigil of the Blood Lamp
item.itemComplexSpellCrystal.name=Complex Spell Crystal
item.itemSigilOfSupression.name=Sigil of Supression
item.itemSigilOfEnderSeverance.name=Sigil of Ender Severance
item.bucketLife.name=Bucket of Life
item.bloodMagicBaseItem.QuartzRod.name=Quartz Rod
item.bloodMagicBaseItem.EmptyCore.name=Empty Core
item.bloodMagicBaseItem.MagicalesCable.name=Magicales Cable
item.bloodMagicBaseItem.WoodBrace.name=Wooden Brace
item.bloodMagicBaseItem.StoneBrace.name=Stone Brace
item.bloodMagicBaseItem.ProjectileCore.name=Projectile Core
item.bloodMagicBaseItem.SelfCore.name=Self Core
item.bloodMagicBaseItem.MeleeCore.name=Melee Core
item.bloodMagicBaseItem.ToolCore.name=Tool Core
item.bloodMagicBaseItem.ParadigmBackPlate.name=Paradigm Plate
item.bloodMagicBaseItem.OutputCable.name=Output Spell Cable
item.bloodMagicBaseItem.InputCable.name=Input Spell Cable
item.bloodMagicBaseItem.FlameCore.name=Fire Core
item.bloodMagicBaseItem.IcyCore.name=Icy Core
item.bloodMagicBaseItem.GustCore.name=Gusty Core
item.bloodMagicBaseItem.EarthenCore.name=Earthen Core
item.bloodMagicBaseItem.CrackedRunicPlate.name=Cracked Runic Plate
item.bloodMagicBaseItem.RunicPlate.name=Runic Plate
item.bloodMagicBaseItem.ScribedRunicPlate.name=Imbued Runic Plate
item.bloodMagicBaseItem.DefaultCore.name=Unattuned Core
item.bloodMagicBaseItem.OffensiveCore.name=Offensive Core
item.bloodMagicBaseItem.DefensiveCore.name=Defensive Core
item.bloodMagicBaseItem.EnvironmentalCore.name=Environmental Core
item.bloodMagicBaseItem.PowerCore.name=Power Core
item.bloodMagicBaseItem.CostCore.name=Reduction Core
item.bloodMagicBaseItem.PotencyCore.name=Potency Core
item.bloodMagicBaseItem.ObsidianBrace.name=Obsidian Brace
item.bloodMagicBaseItem.EtherealSlate.name=Ethereal Slate
item.bloodMagicBaseItem.LifeShard.name=Life Shard
item.bloodMagicBaseItem.SoulShard.name=Soul Shard
item.bloodMagicBaseItem.LifeBrace.name=Living Brace
item.bloodMagicBaseItem.SoulRunicPlate.name=Soul Runic Plate
item.bloodMagicAlchemyItem.Offensa.name=Offensa
item.bloodMagicAlchemyItem.Praesidium.name=Praesidium
item.bloodMagicAlchemyItem.OrbisTerrae.name=Orbis Terrae
item.bloodMagicAlchemyItem.StrengthenedCatalyst.name=Strengthened Catalyst
item.bloodMagicAlchemyItem.ConcentratedCatalyst.name=Concentrated Catalyst
item.bloodMagicAlchemyItem.FracturedBone.name=Fractured Bone
item.bloodMagicAlchemyItem.Virtus.name=Virtus
item.bloodMagicAlchemyItem.Reductus.name=Reductus
item.bloodMagicAlchemyItem.Potentia.name=Potentia
item.sanguineHelmet.name=Sanguine Helmet
item.itemSeerSigil.name=Sigil of Sight
item.itemFluidSigil.name=
item.multiTool.name=Dynamic Mace
item.itemCombinationalCatalyst.name=Combinational Catalyst
item.sanguineRobe.name=Sanguine Robes
item.sanguinePants.name=Sanguine Leggings
item.sanguineBoots.name=Sanguine Boots
item.itemAttunedCrystal.name=Alchemic Router
item.itemTankSegmenter.name=Alchemic Segmenter
item.destinationClearer.name=Alchemic Cleanser
item.demonPlacer.name=Demon Crystal
item.creativeDagger.name=Creative Sacrificial Knife
item.itemBloodPack.name=Blood Letter's Pack
item.itemHarvestSigil.name=Harvest Goddess Sigil
item.itemCompressionSigil.name=Sigil of Compression
item.itemAssassinSigil.name=Sigil of the Assassin
item.transcendentBloodOrb.name=Transcendent Blood Orb
item.itemMailCatalogue.name=Mail Order Catalogue
item.inputRoutingFocus.name=Input Routing Focus
item.bloodMagicBaseItem.EnderShard.name=Ender Shard
item.outputRoutingFocus.default.name=Default Output Routing Focus
item.outputRoutingFocus.modItem.name=Output Routing Focus (ModItem)
item.outputRoutingFocus.ignMeta.name=Output Routing Focus (Ignore Meta)
item.outputRoutingFocus.matchNBT.name=Output Routing Focus (MatchNBT)
item.outputRoutingFocus.global.name=Output Routing Focus (Global)
item.dawnScribeTool.name=Elemental Inscription Tool: Dawn
item.boundHelmetEarth.name=Earth Omega Helmet
item.boundPlateEarth.name=Earth Omega Plate
item.boundLeggingsEarth.name=Earth Omega Leggings
item.boundBootsEarth.name=Earth Omega Boots
item.boundHelmetWind.name=Wind Omega Helmet
item.boundPlateWind.name=Wind Omega Plate
item.boundLeggingsWind.name=Wind Omega Leggings
item.boundBootsWind.name=Wind Omega Boots
item.boundHelmetFire.name=Fire Omega Helmet
item.boundPlateFire.name=Fire Omega Plate
item.boundLeggingsFire.name=Fire Omega Leggings
item.boundBootsFire.name=Fire Omega Boots
item.boundHelmetWater.name=Water Omega Helmet
item.boundPlateWater.name=Water Omega Plate
item.boundLeggingsWater.name=Water Omega Leggings
item.boundBootsWater.name=Water Omega Boots

item.bloodMagicIncenseItem.Woodash.name=Wood Ash
item.bloodMagicIncenseItem.Byrrus.name=Byrrus
item.bloodMagicIncenseItem.Livens.name=Livens
item.bloodMagicIncenseItem.Viridis.name=Viridis
item.bloodMagicIncenseItem.Purpura.name=Purpura

#Creative Tab
itemGroup.tabBloodMagic=Blood Magic

#Extra Strings
bm.string.consume=Usage
bm.string.drain=Drain
bm.string.tier=Tier
bm.string.crafting.orb.shaped=Shaped Orb Crafting
bm.string.crafting.orb.shapeless=Shapeless Orb Crafting
text.recipe.altar=Blood Altar
text.recipe.altar.tier=Tier: %s
text.recipe.altar.bloodRequired=LP: %s
text.recipe.shapedOrb=Shaped Orb Recipe

#Entities
entity.AWWayofTime.EarthElemental.name=Earth Elemental
entity.AWWayofTime.FireElemental.name=Fire Elemental
entity.AWWayofTime.HolyElemental.name=Holy Elemental
entity.AWWayofTime.ShadeElemental.name=Shade Elemental
entity.AWWayofTime.WaterElemental.name=Water Elemental
entity.AWWayofTime.AirElemental.name=Air Elemental
entity.AWWayofTime.Shade.name=Shade
entity.AWWayofTime.BoulderFist.name=Boulder Fist
entity.AWWayofTime.IceDemon.name=Ice Demon
entity.AWWayofTime.SmallEarthGolem.name=Small Earth Golem
entity.AWWayofTime.WingedFireDemon.name=Winged Fire Demon
entity.AWWayofTime.BileDemon.name=Bile Demon
entity.AWWayofTime.LowerGuardian.name=Lower Guardian
entity.AWWayofTime.FallenAngel.name=Fallen Angel
entity.AWWayofTime.MinorDemonGruntGuardian.name=Demon Grunt Guardian
entity.AWWayofTime.MinorDemonGruntGuardianWind.name=Wind Demon Grunt Guardian
entity.AWWayofTime.MinorDemonGruntGuardianFire.name=Fire Demon Grunt Guardian
entity.AWWayofTime.MinorDemonGruntGuardianIce.name=Ice Demon Grunt Guardian
entity.AWWayofTime.MinorDemonGruntGuardianEarth.name=Earth Demon Grunt Guardian
entity.AWWayofTime.MinorDemonGruntWind.name=Wind Demon Grunt
entity.AWWayofTime.MinorDemonGruntFire.name=Fire Demon Grunt
entity.AWWayofTime.MinorDemonGruntIce.name=Ice Demon Grunt
entity.AWWayofTime.MinorDemonGruntEarth.name=Earth Demon Grunt
entity.AWWayofTime.MinorDemonGrunt.name=Demon Grunt

#Commands
commands.error.arg.invalid=Invalid arguments
commands.error.arg.missing=Not enough arguments
commands.error.arg.player.missing=You must specify which player you wish to perform this action on.
commands.error.404=Command not found!
commands.error.unknown=Unknown command!
commands.error.perm=You do not have permission to use this command.

commands.success=Executed successfully

commands.format.help=%s - %s
commands.format.error=%s - %s

commands.help.usage=/bloodmagic help
commands.help.help=Displays the help information for the "/bloodmagic" command.

commands.network.usage=/bloodmagic network [syphon|add|get|fill|cap] player [amount]
commands.network.help=LP network utilities
commands.network.syphon.help=Removes the given amount of LP from the given player's LP network.
commands.network.syphon.success=Successfully syphoned %d LP from %s.
commands.network.add.help=Adds the given amount of LP to the given player's LP network. Follows standard LP gain rules.
commands.network.add.success=Successfully added %d LP to %s's LP network.
commands.network.set.help=Sets the given player's LP to the given amount.
commands.network.set.success=Successfully set %s's LP network to %d LP.
commands.network.get.help=Returns the amount of LP in the given player's LP network.
commands.network.fill.help=Fills the given player's LP network to %d.
commands.network.fill.success=Successfully filled %s's LP network.
commands.network.cap.help=Fills the given player's LP network to the max that their highest Blood Orb can store.
commands.network.cap.success=Successfully capped off %s's LP network.

commands.bind.usage=/bloodmagic bind [true|false] [player]
commands.bind.help=Attempts to (un)bind the currently held item.
commands.bind.success=Binding successful
commands.bind.remove.success=Unbinding successful

commands.orb.usage=/bloodmagic orb [set|get] player [tier]
commands.orb.help=Used to set or get the Player's max Blood Orb tier.

commands.bind.usage=/bind <player>
commands.bind.success=Item successfully bound!
commands.bind.failed.noPlayer=There is no player specified
commands.bind.failed.alreadyBound=Item is already bound; use /unbind to unbind it
commands.bind.failed.notBindable=Item cannot be bound
commands.unbind.usage=/unbind
commands.unbind.success=Item successfully unbound!
commands.unbind.failed.notBindable=Item cannot be unbound
commands.soulnetwork.usage=/soulnetwork <player> <add|subtract|fill|empty|get> [amount]
commands.soulnetwork.add.success=Successfully added %dLP to %s's Soul Network!
commands.soulnetwork.subtract.success=Successfully subtracted %dLP from %s's Soul Network!
commands.soulnetwork.fill.success=Successfully filled %s's Soul Network!
commands.soulnetwork.empty.success=Successfully emptied %s's Soul Network!
commands.soulnetwork.get.success=There is %dLP in %s's Soul Network!
commands.soulnetwork.noPlayer=There is no player specified
commands.soulnetwork.noCommand=There is no command specified
commands.soulnetwork.notACommand=That is not a valid command
commands.soulnetwork.fillMax.success=Successfully filled %s's Soul Network to their orb max!
commands.soulnetwork.create.success=Successfully created %s's Soul Network (Orb tier: %d)

#Tooltips
tooltip.activationcrystal.creativeonly=Creative Only - activates any ritual
tooltip.activationcrystal.lowlevelrituals=Activates low-level rituals
tooltip.activationcrystal.powerfulrituals=Activates more powerful rituals
tooltip.airsigil.desc=I feel lighter already...
tooltip.alchemy.coords=Coords:
tooltip.alchemy.damage=Damage:
tooltip.alchemy.dimension=Bound Dimension:
tooltip.alchemy.direction=Direction:
tooltip.alchemy.forrecipe=for Recipe
tooltip.alchemy.press=Press
tooltip.alchemy.recipe=Recipe:
tooltip.alchemy.ritualid=RitualID:
tooltip.alchemy.shift=shift
tooltip.alchemy.usedinalchemy=Used in Alchemy
tooltip.alchemyflask.caution=CAUTION: Contents are throwable
tooltip.alchemyflask.swigsleft=Swigs Left:
tooltip.armorinhibitor.desc1=Used to suppress a soul's
tooltip.armorinhibitor.desc2=unnatural abilities.
tooltip.attunedcrystal.desc1=A tool to tune alchemy
tooltip.attunedcrystal.desc2=reagent transmission
tooltip.blankspell.desc=Crystal of infinite possibilities.
tooltip.bloodframe.desc=Stirs bees into a frenzy.
tooltip.bloodletterpack.desc=This pack really chaffs...
tooltip.bloodlightsigil.desc=I see a light!
tooltip.boundarmor.devprotect=Devilish Protection
tooltip.boundaxe.desc=Axe me about my puns!
tooltip.boundpickaxe.desc1=The Souls of the Damned
tooltip.boundpickaxe.desc2=do not like stone...
tooltip.boundshovel.desc=No, not that type of spade.
tooltip.caution.desc1=Caution: may cause
tooltip.caution.desc2=a bad day...
tooltip.cheatyitem.desc1=Right-click to fill network,
tooltip.cheatyitem.desc2=shift-right to empty.
tooltip.complexspellcrystal.desc=Crystal of unimaginable power
tooltip.crystalbelljar.contents=Current Contents:
tooltip.crystalbelljar.empty=- Empty
tooltip.demonictelepfocus.desc1=A stronger version of the focus,
tooltip.demonictelepfocus.desc2=using a demonic shard
tooltip.demonplacer.desc=Used to spawn demons.
tooltip.destclearer.desc1=Used to clear the destination
tooltip.destclearer.desc2=list for an alchemy container
tooltip.diablokey.desc=Binds other items to the owner's network
tooltip.divinationsigil.desc1=Peer into the soul to
tooltip.divinationsigil.desc2=get the current essence
tooltip.energybazooka.desc=Boom.
tooltip.energybattery.desc=Stores raw Life Essence
tooltip.energyblast.desc1=Used to fire devastating
tooltip.energyblast.desc2=projectiles.
tooltip.enhancedtelepfocus.desc=A focus further enhanced in an altar
tooltip.fluidsigil.beastmode=Beast Mode
tooltip.fluidsigil.desc=A sigil with a lovely affinity for fluids
tooltip.fluidsigil.draintankmode=Drain Tank Mode
tooltip.fluidsigil.filltankmode=Fill Tank Mode
tooltip.fluidsigil.fluidplacementmode=Fluid Placement Mode
tooltip.fluidsigil.forcesyphonmode=Force-syphon Mode
tooltip.fluidsigil.syphoningmode=Syphoning Mode
tooltip.harvestsigil.desc=You sow what you reap
tooltip.infusedstone.desc1=Infused stone inside of
tooltip.infusedstone.desc2=a blood altar
tooltip.item.iteminslot=Item in slot
tooltip.item.currentitem=Current Item:
tooltip.lavacrystal.desc1=Store life to smelt
tooltip.lavacrystal.desc2=stuff in the furnace.
tooltip.lavasigil.desc1=Contact with liquid is
tooltip.lavasigil.desc2=highly unrecommended.
tooltip.lp.storedlp=Stored LP:
tooltip.mode.creative=Creative Only
tooltip.owner.currentowner=Current Owner:
tooltip.owner.demonsowner=Demon's Owner:
tooltip.packratsigil.desc=Hands of Diamonds
tooltip.reagent.selectedreagent=Currently selected reagent:
tooltip.reinforcedtelepfocus.desc1=A stronger version of the focus,
tooltip.reinforcedtelepfocus.desc2=using a weak shard
tooltip.ritualdiviner.airstones=Air Stones:
tooltip.ritualdiviner.blankstones=Blank Stones:
tooltip.ritualdiviner.cannotplace=Can not place Dusk runes
tooltip.ritualdiviner.canplace=Can place Dusk runes
tooltip.ritualdiviner.canplacedawn=Can place Dusk and Dawn runes
tooltip.ritualdiviner.desc=Used to explore new types of rituals
tooltip.ritualdiviner.duskstones=Dusk Stones:
tooltip.ritualdiviner.earthstones=Earth Stones:
tooltip.ritualdiviner.firestones=Fire Stones:
tooltip.ritualdiviner.moreinfo=Press shift for extended information
tooltip.ritualdiviner.ritualtunedto=Ritual tuned to face:
tooltip.ritualdiviner.waterstones=Water Stones:
tooltip.ritualdiviner.dawnstones=Dawn Stones:
tooltip.ritualdiviner.totalStones=Total Stones:
tooltip.dismatler.desc=You can turn this upside down?
tooltip.sacrificialdagger.desc1=A slight draining feeling tickles your fingers
tooltip.sacrificialdagger.desc2=Just a prick of the
tooltip.sacrificialdagger.desc3=finger will suffice...
tooltip.sanguinearmor.desc1=A pair of goggles imbued with power
tooltip.sanguinearmor.desc2=Robes imbued with forbidden power
tooltip.sanguinearmor.visdisc=Vis discount:
tooltip.scribetool.desc=The writing is on the wall...
tooltip.seersigil.desc=When seeing all is not enough
tooltip.sigilofelementalaffinity.desc1=Perfect for a fire-breathing fish
tooltip.sigilofelementalaffinity.desc2=who is afraid of heights!
tooltip.sigilofenderseverance.desc=Put those endermen in a Dire situation!
tooltip.sigilofgrowth.desc1=Who needs a green thumb when
tooltip.sigilofgrowth.desc2=you have a green slate?
tooltip.sigilofhaste.desc=One dose of caffeine later...
tooltip.sigilofholding.desc=Used to hold several Sigils!
tooltip.sigilofmagnetism.desc=I have a very magnetic personality!
tooltip.sigilofsupression.desc=Better than telekinesis
tooltip.sigiloftheassassin.desc=Time to stay stealthy...
tooltip.sigilofthebridge.desc1=Activate to create a bridge
tooltip.sigilofthebridge.desc2=beneath your feet.
tooltip.sigilofthefastminer.desc=Keep going and going and going...
tooltip.sigilofwind.desc=Best not to wear a skirt.
tooltip.sigil.state.activated=Activated
tooltip.sigil.state.deactivated=Deactivated
tooltip.tanksegmenter.desc1=Used to designate which
tooltip.tanksegmenter.desc2=reagents can go into a container
tooltip.telepositionfocus.desc=An Enderpearl imbued with blood
tooltip.voidsigil.desc=Better than a Swiffer!
tooltip.watersigil.desc=Infinite water, anyone?
tooltip.routingFocus.limit=Limit:
tooltip.routingFocus.desc=A focus used to route items
tooltip.alchemy.usedinincense=Purifying incense used in a crucible

#Messages
message.altar.capacity=Capacity: %s LP
message.altar.consumptionrate=Consumption Rate:
message.altar.currentessence=Altar's Current Essence: %s LP
message.altar.currenttier=Altar's Current Tier: %s
message.altar.progress=Altar's Progress:
message.altar.inputtank= Input Tank: %s LP
message.altar.outputtank= Output Tank: %s LP
message.altar.hunger=[BM] Your high regeneration rate has caused you to become hungry...
message.attunedcrystal.clearing=Clearing saved container...
message.attunedcrystal.error.cannotfind=Can no longer find linked container.
message.attunedcrystal.error.noconnections=Linked container has no connections remaining!
message.attunedcrystal.error.toofar=Linked container is either too far or is in a different dimension.
message.attunedcrystal.linked=Container is now linked. Transmitting:
message.attunedcrystal.linking=Linking to selected container.
message.attunedcrystal.setto=Attuned Crystal now set to:
message.demon.shallfollow=I shall follow and protect you!
message.demon.willstay=I will stay here for now, Master.
message.destinationclearer.cleared=Destination list now cleared.
message.divinationsigil.amount=Amount:
message.divinationsigil.currentessence=Current Essence:
message.divinationsigil.reagent=Reagent:
message.orb.currenttier=Current Tier: %d
message.masterstone.crystalvibrates=Your crystal vibrates pathetically.
message.masterstone.energyflows=A rush of energy flows through the ritual!
message.masterstone.nothinghappened=Nothing appears to have happened...
message.masterstone.ritualresistyou=The ritual appears to actively resist you!
message.masterstone.somethingstoppedyou=Something stopped you in your tracks...
message.masterstone.youfeelapull=You feel a pull, but you are too weak to push any further.
message.ritual.currentritual=Current Ritual:
message.ritual.side.east=EAST
message.ritual.side.north=NORTH
message.ritual.side.south=SOUTH
message.ritual.side.west=WEST
message.ritualdemonportal.missingjar=A jar on one of the pillars appears to be missing...
message.tanksegmenter.nowhas=Tank now has
message.tanksegmenter.setto=Tank Segmenter now set to:
message.tanksegmenter.tankssetto=tank(s) set to:
message.routerfocus.limit=Focus' Item Limit set to: 

#Achievements
achievement.alchemicalwizardry:firstPrick=Your First Prick!
achievement.alchemicalwizardry:firstPrick.desc=Craft a Sacrificial Knife/Orb
achievement.alchemicalwizardry:weakOrb=Faintly Glowing Red
achievement.alchemicalwizardry:weakOrb.desc=Pickup a Weak Blood Orb
achievement.alchemicalwizardry:bloodLettersPack=Gathering More Blood
achievement.alchemicalwizardry:bloodLettersPack.desc=Create a Blood Letter's Pack
achievement.alchemicalwizardry:waterSigil=Infinite Water!
achievement.alchemicalwizardry:waterSigil.desc=Make a Water Sigil with your Weak Blood Orb
achievement.alchemicalwizardry:blankRunes=Stepping Towards Tier 2
achievement.alchemicalwizardry:blankRunes.desc=Make a couple of Blank Runes
achievement.alchemicalwizardry:apprenticeOrb=Getting Stronger
achievement.alchemicalwizardry:apprenticeOrb.desc=Pickup an Apprentice Orb
achievement.alchemicalwizardry:airSigil=Whoosh!
achievement.alchemicalwizardry:airSigil.desc=Craft an Air Sigil
achievement.alchemicalwizardry:daggerSacrifice=Sacrificing Others
achievement.alchemicalwizardry:daggerSacrifice.desc=Pickup a Dagger of Sacrifice
achievement.alchemicalwizardry:brewingPotions=Alchemical Chemistry
achievement.alchemicalwizardry:brewingPotions.desc=Craft an Alchemical Chemistry set
achievement.alchemicalwizardry:magicianOrb=Even Stronger
achievement.alchemicalwizardry:magicianOrb.desc=Pickup a Magician's Orb
achievement.alchemicalwizardry:sigilHolding=Hold All The Sigils!
achievement.alchemicalwizardry:sigilHolding.desc=Make a Sigil of Holding
achievement.alchemicalwizardry:boundBlade=More Pain
achievement.alchemicalwizardry:boundBlade.desc=Create a Bound Blade from a Ritual of Binding
achievement.alchemicalwizardry:boundArmor=Uncrackable
achievement.alchemicalwizardry:boundArmor.desc=Craft Bound Armor from the Armor Forge
achievement.alchemicalwizardry:complexSpells=Not Quite Hemomancy
achievement.alchemicalwizardry:complexSpells.desc=Pickup a Complex Spell Crystal
achievement.alchemicalwizardry:ritualDiviner=Ritual Making
achievement.alchemicalwizardry:ritualDiviner.desc=Craft a Ritual Diviner
achievement.alchemicalwizardry:masterOrb=Getting Very Strong
achievement.alchemicalwizardry:masterOrb.desc=Pickup a Master Orb
achievement.alchemicalwizardry:demonSpawn=Demon Spawn
achievement.alchemicalwizardry:demonSpawn.desc=Summon and slay a demon/elemental
achievement.alchemicalwizardry:phantomBridgeSigil=Walking In Mid-Air
achievement.alchemicalwizardry:phantomBridgeSigil.desc=Make the Sigil of the Phantom Bridge
achievement.alchemicalwizardry:teleposer=Teleposed!
achievement.alchemicalwizardry:teleposer.desc=Craft a Teleposer
achievement.alchemicalwizardry:suppressionSigil=Suppress!
achievement.alchemicalwizardry:suppressionSigil.desc=Create a Suppression Sigil
achievement.alchemicalwizardry:archmageOrb=Too Strong
achievement.alchemicalwizardry:archmageOrb.desc=Pickup an Archmage Orb
achievement.alchemicalwizardry:energyBazooka=BOOM!!!
achievement.alchemicalwizardry:energyBazooka.desc=Create the all powerful, Energy Bazooka!!
achievement.alchemicalwizardry:demons=Demon Hunter
achievement.alchemicalwizardry:demons.desc=Kill a Demon
achievement.alchemicalwizardry:trancsendentOrb=OVERPOWERED
achievement.alchemicalwizardry:trancsendentOrb.desc=Pickup a Transcendent Blood Orb

#G-API Downloading
bm.versioning.getGAPI=["[BM] You don't have Guide-API installed! Install it to get it to unlock the book! [",{"text":"Download","color":"red","hoverEvent":{"action":"show_text","value":{"text":"Click this to auto-magically download the latest version","color":"red"}},"clickEvent":{"action":"run_command","value":"/bloodmagic-download-g-api"}},"]"]
bm.versioning.startingDownload=[{"text":"Starting download of %s Please do not remove your hard disk.", "color":"orange"}]
bm.versioning.doneDownloading=Finished downloading %s Reload your game to update.
bm.versioning.error=An error has occurred while downloading the mod!
bm.versioning.downloadedAlready=You have the latest version already, reload your game to update!
bm.versioning.downloadingAlready=It's downloading! Be patient!
bm.versioning.disabled=This feature is disabled.
