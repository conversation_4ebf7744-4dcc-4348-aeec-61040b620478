package wayoftime.bloodmagic.core.living;

import net.minecraft.world.entity.player.Player;
import net.minecraft.world.damagesource.DamageSource;

public class ProjectileArmorProvider implements LivingUpgrade.IArmorProvider
{
	@Override
	public double getProtection(Player player, LivingStats stats, DamageSource source, LivingUpgrade upgrade, int level)
	{
		// TODO Auto-generated method stub
		return 0;
	}

}
