package wayoftime.bloodmagic.compat.structurelib;

import wayoftime.bloodmagic.util.BMLog;

/**
 * Compatibility class for StructureLib integration
 * This replaces the problematic Java Records implementation with traditional classes
 */
public class CompatStructureLib {
    
    public static void init() {
        try {
            // Check if StructureLib is available
            Class.forName("com.gtnewhorizon.structurelib.StructureLibAPI");
            
            // Register altar structures if StructureLib is present
            AltarStructure.registerAltarStructureInfo();
            
            BMLog.DEFAULT.info("StructureLib compatibility initialized successfully");
        } catch (ClassNotFoundException e) {
            // StructureLib not present, skip initialization
            BMLog.DEFAULT.info("StructureLib not found, skipping compatibility initialization");
        } catch (Exception e) {
            // Log error but don't crash
            BMLog.DEFAULT.warn("Failed to initialize StructureLib compatibility: " + e.getMessage());
        }
    }
}
